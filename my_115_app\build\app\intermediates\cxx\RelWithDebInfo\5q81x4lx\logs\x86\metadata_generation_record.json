[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: x86", "file_": "D:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "productionRelease|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON D:\\flutter-workspace\\my_115_app\\build\\.cxx\\RelWithDebInfo\\5q81x4lx\\x86\\android_gradle_build.json due to:", "file_": "D:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "productionRelease|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "D:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "productionRelease|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'D:\\flutter-workspace\\my_115_app\\build\\.cxx\\RelWithDebInfo\\5q81x4lx\\x86'", "file_": "D:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "productionRelease|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'D:\\flutter-workspace\\my_115_app\\build\\.cxx\\RelWithDebInfo\\5q81x4lx\\x86'", "file_": "D:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "productionRelease|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\Flutter\\\\flutter\\\\packages\\\\flutter_tools\\\\gradle\\\\src\\\\main\\\\scripts\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=21\" ^\n  \"-DANDROID_PLATFORM=android-21\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\27.0.12077973\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\flutter-workspace\\\\my_115_app\\\\build\\\\app\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\5q81x4lx\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\flutter-workspace\\\\my_115_app\\\\build\\\\app\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\5q81x4lx\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=RelWithDebInfo\" ^\n  \"-BD:\\\\flutter-workspace\\\\my_115_app\\\\build\\\\.cxx\\\\RelWithDebInfo\\\\5q81x4lx\\\\x86\" ^\n  -GNinja ^\n  -Wno-dev ^\n  --no-warn-unused-cli\n", "file_": "D:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "productionRelease|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\Flutter\\\\flutter\\\\packages\\\\flutter_tools\\\\gradle\\\\src\\\\main\\\\scripts\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=21\" ^\n  \"-DANDROID_PLATFORM=android-21\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\27.0.12077973\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\flutter-workspace\\\\my_115_app\\\\build\\\\app\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\5q81x4lx\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\flutter-workspace\\\\my_115_app\\\\build\\\\app\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\5q81x4lx\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=RelWithDebInfo\" ^\n  \"-BD:\\\\flutter-workspace\\\\my_115_app\\\\build\\\\.cxx\\\\RelWithDebInfo\\\\5q81x4lx\\\\x86\" ^\n  -GNinja ^\n  -Wno-dev ^\n  --no-warn-unused-cli\n", "file_": "D:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "productionRelease|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "productionRelease|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "D:\\flutter-workspace\\my_115_app\\build\\.cxx\\RelWithDebInfo\\5q81x4lx\\x86\\compile_commands.json.bin existed but not D:\\flutter-workspace\\my_115_app\\build\\.cxx\\RelWithDebInfo\\5q81x4lx\\x86\\compile_commands.json", "file_": "D:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "productionRelease|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "D:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "productionRelease|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "productionRelease|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]