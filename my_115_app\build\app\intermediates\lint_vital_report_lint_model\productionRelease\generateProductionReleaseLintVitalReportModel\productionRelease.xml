<variant
    name="productionRelease"
    package="com.example.my_115_app"
    minSdkVersion="21"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="D:\flutter-workspace\my_115_app\build\app\intermediates\merged_manifest\productionRelease\processProductionReleaseMainManifest\AndroidManifest.xml"
    manifestMergeReport="D:\flutter-workspace\my_115_app\build\app\outputs\logs\manifest-merger-production-release-report.txt"
    proguardFiles="D:\flutter-workspace\my_115_app\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.3;D:\Flutter\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="D:\flutter-workspace\my_115_app\build\app\intermediates\lint_vital_partial_results\productionRelease\lintVitalAnalyzeProductionRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\production\java;src\release\java;src\productionRelease\java;src\main\kotlin;src\production\kotlin;src\release\kotlin;src\productionRelease\kotlin"
        resDirectories="src\main\res;src\production\res;src\release\res;src\productionRelease\res"
        assetsDirectories="src\main\assets;src\production\assets;src\release\assets;src\productionRelease\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <resValues>
    <resValue
        type="string"
        name="app_name"
        value="My 115 App" />
  </resValues>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="D:\flutter-workspace\my_115_app\build\app\intermediates\javac\productionRelease\compileProductionReleaseJavaWithJavac\classes;D:\flutter-workspace\my_115_app\build\app\tmp\kotlin-classes\productionRelease;D:\flutter-workspace\my_115_app\build\app\kotlinToolingMetadata;D:\flutter-workspace\my_115_app\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\productionRelease\processProductionReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.example.my_115_app"
      generatedSourceFolders="D:\flutter-workspace\my_115_app\build\app\generated\ap_generated_sources\productionRelease\out"
      generatedResourceFolders="D:\flutter-workspace\my_115_app\build\app\generated\res\resValues\production\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\7ff7c222f72a39cd2d6660f4156e5aff\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
