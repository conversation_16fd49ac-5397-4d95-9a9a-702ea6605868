1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.my_115_app"
4    android:versionCode="1"
5    android:versionName="1.0.4" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!-- 网络权限 -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:3:5-67
11-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:4:5-79
12-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:4:22-76
13
14    <!-- 存储权限（用于图片缓存） -->
15    <uses-permission
15-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:7:5-8:51
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:7:22-78
17        android:maxSdkVersion="29" />
17-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:8:22-48
18    <uses-permission
18-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:9:5-10:51
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:9:22-77
20        android:maxSdkVersion="32" />
20-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:10:22-48
21
22    <!-- 视频播放器权限 -->
23    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
23-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:13:5-80
23-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:13:22-77
24    <uses-permission android:name="android.permission.VIBRATE" />
24-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:14:5-66
24-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:14:22-63
25    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
25-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:15:5-17:60
25-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:15:22-70
26    <uses-permission android:name="android.permission.CAMERA" />
26-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:18:5-65
26-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:18:22-62
27    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
27-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:19:5-76
27-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:19:22-73
28    <!--
29         Required to query activities that can process text, see:
30         https://developer.android.com/training/package-visibility and
31         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
32
33         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
34    -->
35    <queries>
35-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:60:5-65:15
36        <intent>
36-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:61:9-64:18
37            <action android:name="android.intent.action.PROCESS_TEXT" />
37-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:62:13-72
37-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:62:21-70
38
39            <data android:mimeType="text/plain" />
39-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:63:13-50
39-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:63:19-48
40        </intent>
41    </queries>
42
43    <permission
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
44        android:name="com.example.my_115_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.example.my_115_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
48
49    <application
50        android:name="android.app.Application"
50-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:23:9-42
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
52        android:extractNativeLibs="true"
53        android:icon="@mipmap/ic_launcher"
53-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:24:9-43
54        android:label="@string/app_name"
54-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:22:9-41
55        android:requestLegacyExternalStorage="true"
55-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:25:9-52
56        android:usesCleartextTraffic="true" >
56-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:26:9-44
57        <activity
57-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:27:9-48:20
58            android:name="com.example.my_115_app.MainActivity"
58-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:28:13-41
59            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
59-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:33:13-163
60            android:exported="true"
60-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:29:13-36
61            android:hardwareAccelerated="true"
61-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:34:13-47
62            android:launchMode="singleTop"
62-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:30:13-43
63            android:taskAffinity=""
63-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:31:13-36
64            android:theme="@style/LaunchTheme"
64-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:32:13-47
65            android:windowSoftInputMode="adjustResize" >
65-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:35:13-55
66
67            <!--
68                 Specifies an Android theme to apply to this Activity as soon as
69                 the Android process has started. This theme is visible to the user
70                 while the Flutter UI initializes. After that, this theme continues
71                 to determine the Window background behind the Flutter UI.
72            -->
73            <meta-data
73-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:40:13-43:17
74                android:name="io.flutter.embedding.android.NormalTheme"
74-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:41:15-70
75                android:resource="@style/NormalTheme" />
75-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:42:15-52
76
77            <intent-filter>
77-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:44:13-47:29
78                <action android:name="android.intent.action.MAIN" />
78-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:45:17-68
78-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:45:25-66
79
80                <category android:name="android.intent.category.LAUNCHER" />
80-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:46:17-76
80-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:46:27-74
81            </intent-filter>
82        </activity>
83        <!--
84             Don't delete the meta-data below.
85             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
86        -->
87        <meta-data
87-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:51:9-53:33
88            android:name="flutterEmbedding"
88-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:52:13-44
89            android:value="2" />
89-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:53:13-30
90        <!--
91           Declares a provider which allows us to store files to share in
92           '.../caches/share_plus' and grant the receiving action access
93        -->
94        <provider
94-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:9-21:20
95            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
95-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-77
96            android:authorities="com.example.my_115_app.flutter.share_provider"
96-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-74
97            android:exported="false"
97-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
98            android:grantUriPermissions="true" >
98-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-47
99            <meta-data
99-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-20:68
100                android:name="android.support.FILE_PROVIDER_PATHS"
100-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:17-67
101                android:resource="@xml/flutter_share_file_paths" />
101-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:17-65
102        </provider>
103        <!--
104           This manifest declared broadcast receiver allows us to use an explicit
105           Intent when creating a PendingItent to be informed of the user's choice
106        -->
107        <receiver
107-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-32:20
108            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
108-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-82
109            android:exported="false" >
109-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-37
110            <intent-filter>
110-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-31:29
111                <action android:name="EXTRA_CHOSEN_COMPONENT" />
111-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-65
111-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:25-62
112            </intent-filter>
113        </receiver>
114
115        <uses-library
115-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
116            android:name="androidx.window.extensions"
116-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
117            android:required="false" />
117-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
118        <uses-library
118-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
119            android:name="androidx.window.sidecar"
119-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
120            android:required="false" />
120-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
121
122        <provider
122-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
123            android:name="androidx.startup.InitializationProvider"
123-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
124            android:authorities="com.example.my_115_app.androidx-startup"
124-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
125            android:exported="false" >
125-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
126            <meta-data
126-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
128                android:value="androidx.startup" />
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
129            <meta-data
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
131                android:value="androidx.startup" />
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
132        </provider>
133
134        <receiver
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
135            android:name="androidx.profileinstaller.ProfileInstallReceiver"
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
136            android:directBootAware="false"
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
137            android:enabled="true"
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
138            android:exported="true"
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
139            android:permission="android.permission.DUMP" >
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
140            <intent-filter>
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
141                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
142            </intent-filter>
143            <intent-filter>
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
144                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
145            </intent-filter>
146            <intent-filter>
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
147                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
148            </intent-filter>
149            <intent-filter>
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
150                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
151            </intent-filter>
152        </receiver>
153    </application>
154
155</manifest>
