<variant
    name="release"
    package="com.alexmercerind.media_kit_video"
    minSdkVersion="16"
    targetSdkVersion="16"
    mergedManifest="D:\flutter-workspace\my_115_app\build\media_kit_video\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    manifestMergeReport="D:\flutter-workspace\my_115_app\build\media_kit_video\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="D:\flutter-workspace\my_115_app\build\media_kit_video\intermediates\default_proguard_files\global\proguard-android.txt-8.7.3"
    consumerProguardFiles="proguard-rules.pro"
    partialResultsDir="D:\flutter-workspace\my_115_app\build\media_kit_video\intermediates\lint_partial_results\release\lintAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="D:\flutter-workspace\my_115_app\build\media_kit_video\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\flutter-workspace\my_115_app\build\media_kit_video\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="com.alexmercerind.media_kit_video"
      generatedSourceFolders="D:\flutter-workspace\my_115_app\build\media_kit_video\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\flutter-workspace\my_115_app\build\media_kit_video\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\0ba2b84671a68b7a206f6fa8e3f854a9\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
