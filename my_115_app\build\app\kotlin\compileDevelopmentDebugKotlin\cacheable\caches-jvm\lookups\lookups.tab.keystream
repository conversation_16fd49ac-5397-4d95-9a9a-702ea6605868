  	Exception android.app.Activity  Int android.app.Activity  
MethodChannel android.app.Activity  Mobile android.app.Activity  String android.app.Activity  stackTraceToString android.app.Activity  	Exception android.content.Context  Int android.content.Context  
MethodChannel android.content.Context  Mobile android.content.Context  String android.content.Context  stackTraceToString android.content.Context  	Exception android.content.ContextWrapper  Int android.content.ContextWrapper  
MethodChannel android.content.ContextWrapper  Mobile android.content.ContextWrapper  String android.content.ContextWrapper  stackTraceToString android.content.ContextWrapper  	Exception  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  
MethodChannel  android.view.ContextThemeWrapper  Mobile  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  stackTraceToString  android.view.ContextThemeWrapper  	Exception com.example.my_115_app  FlutterActivity com.example.my_115_app  
FlutterEngine com.example.my_115_app  Int com.example.my_115_app  MainActivity com.example.my_115_app  
MethodChannel com.example.my_115_app  Mobile com.example.my_115_app  String com.example.my_115_app  stackTraceToString com.example.my_115_app  CHANNEL #com.example.my_115_app.MainActivity  
MethodChannel #com.example.my_115_app.MainActivity  Mobile #com.example.my_115_app.MainActivity  stackTraceToString #com.example.my_115_app.MainActivity  FlutterActivity io.flutter.embedding.android  	Exception ,io.flutter.embedding.android.FlutterActivity  Int ,io.flutter.embedding.android.FlutterActivity  
MethodChannel ,io.flutter.embedding.android.FlutterActivity  Mobile ,io.flutter.embedding.android.FlutterActivity  String ,io.flutter.embedding.android.FlutterActivity  configureFlutterEngine ,io.flutter.embedding.android.FlutterActivity  stackTraceToString ,io.flutter.embedding.android.FlutterActivity  
FlutterEngine io.flutter.embedding.engine  dartExecutor )io.flutter.embedding.engine.FlutterEngine  DartExecutor  io.flutter.embedding.engine.dart  binaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  <SAM-CONSTRUCTOR> 8io.flutter.plugin.common.MethodChannel.MethodCallHandler  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  	Exception 	java.lang  message java.lang.Exception  stackTraceToString java.lang.Exception  	Function2 kotlin  Nothing kotlin  stackTraceToString kotlin  toLong 
kotlin.Int  message kotlin.Throwable  Mobile mobile  getProxyURL 
mobile.Mobile  isServerRunning 
mobile.Mobile  startProxyServer 
mobile.Mobile  stopProxyServer 
mobile.Mobile                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                