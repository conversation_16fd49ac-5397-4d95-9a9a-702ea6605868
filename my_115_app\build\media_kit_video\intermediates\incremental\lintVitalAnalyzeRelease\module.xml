<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\media_kit_video-1.3.0\android"
    name=":media_kit_video"
    type="LIBRARY"
    maven="com.alexmercerind.media_kit_video:media_kit_video:1.0"
    agpVersion="8.7.3"
    buildFolder="D:\flutter-workspace\my_115_app\build\media_kit_video"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
