1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.my_115_app.debug"
4    android:versionCode="1"
5    android:versionName="1.0.4" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:3:5-67
15-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 存储权限（用于图片缓存） -->
16-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:4:5-79
16-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:4:22-76
17    <uses-permission
17-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:7:5-8:51
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:7:22-78
19        android:maxSdkVersion="29" />
19-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:8:22-48
20    <uses-permission
20-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:9:5-10:51
21        android:name="android.permission.READ_EXTERNAL_STORAGE"
21-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:9:22-77
22        android:maxSdkVersion="32" /> <!-- 视频播放器权限 -->
22-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:10:22-48
23    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
23-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:13:5-80
23-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:13:22-77
24    <uses-permission android:name="android.permission.VIBRATE" />
24-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:14:5-66
24-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:14:22-63
25    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
25-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:15:5-17:60
25-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:15:22-70
26    <uses-permission android:name="android.permission.CAMERA" />
26-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:18:5-65
26-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:18:22-62
27    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
27-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:19:5-76
27-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:19:22-73
28    <!--
29 Required to query activities that can process text, see:
30         https://developer.android.com/training/package-visibility and
31         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
32
33         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
34    -->
35    <queries>
35-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:60:5-65:15
36        <intent>
36-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:61:9-64:18
37            <action android:name="android.intent.action.PROCESS_TEXT" />
37-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:62:13-72
37-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:62:21-70
38
39            <data android:mimeType="text/plain" />
39-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:63:13-50
39-->D:\flutter-workspace\my_115_app\android\app\src\main\AndroidManifest.xml:63:19-48
40        </intent>
41    </queries>
42
43    <permission
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
44        android:name="com.example.my_115_app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.example.my_115_app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
48
49    <application
50        android:name="android.app.Application"
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
52        android:debuggable="true"
53        android:extractNativeLibs="true"
54        android:icon="@mipmap/ic_launcher"
55        android:label="@string/app_name"
56        android:requestLegacyExternalStorage="true"
57        android:usesCleartextTraffic="true" >
58        <activity
59            android:name="com.example.my_115_app.MainActivity"
60            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
61            android:exported="true"
62            android:hardwareAccelerated="true"
63            android:launchMode="singleTop"
64            android:taskAffinity=""
65            android:theme="@style/LaunchTheme"
66            android:windowSoftInputMode="adjustResize" >
67
68            <!--
69                 Specifies an Android theme to apply to this Activity as soon as
70                 the Android process has started. This theme is visible to the user
71                 while the Flutter UI initializes. After that, this theme continues
72                 to determine the Window background behind the Flutter UI.
73            -->
74            <meta-data
75                android:name="io.flutter.embedding.android.NormalTheme"
76                android:resource="@style/NormalTheme" />
77
78            <intent-filter>
79                <action android:name="android.intent.action.MAIN" />
80
81                <category android:name="android.intent.category.LAUNCHER" />
82            </intent-filter>
83        </activity>
84        <!--
85             Don't delete the meta-data below.
86             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
87        -->
88        <meta-data
89            android:name="flutterEmbedding"
90            android:value="2" />
91        <!--
92           Declares a provider which allows us to store files to share in
93           '.../caches/share_plus' and grant the receiving action access
94        -->
95        <provider
95-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
96            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
96-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
97            android:authorities="com.example.my_115_app.debug.flutter.share_provider"
97-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
98            android:exported="false"
98-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
99            android:grantUriPermissions="true" >
99-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
100            <meta-data
100-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
101                android:name="android.support.FILE_PROVIDER_PATHS"
101-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
102                android:resource="@xml/flutter_share_file_paths" />
102-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
103        </provider>
104        <!--
105           This manifest declared broadcast receiver allows us to use an explicit
106           Intent when creating a PendingItent to be informed of the user's choice
107        -->
108        <receiver
108-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
109            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
109-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
110            android:exported="false" >
110-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
111            <intent-filter>
111-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
112                <action android:name="EXTRA_CHOSEN_COMPONENT" />
112-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
112-->[:share_plus] D:\flutter-workspace\my_115_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
113            </intent-filter>
114        </receiver>
115
116        <uses-library
116-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
117            android:name="androidx.window.extensions"
117-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
118            android:required="false" />
118-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
119        <uses-library
119-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
120            android:name="androidx.window.sidecar"
120-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
121            android:required="false" />
121-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
122
123        <provider
123-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
124            android:name="androidx.startup.InitializationProvider"
124-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
125            android:authorities="com.example.my_115_app.debug.androidx-startup"
125-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
126            android:exported="false" >
126-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
127            <meta-data
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
128                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
129                android:value="androidx.startup" />
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
130            <meta-data
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
131                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
132                android:value="androidx.startup" />
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
133        </provider>
134
135        <receiver
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
136            android:name="androidx.profileinstaller.ProfileInstallReceiver"
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
137            android:directBootAware="false"
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
138            android:enabled="true"
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
139            android:exported="true"
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
140            android:permission="android.permission.DUMP" >
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
142                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
143            </intent-filter>
144            <intent-filter>
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
145                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
146            </intent-filter>
147            <intent-filter>
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
148                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
149            </intent-filter>
150            <intent-filter>
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
151                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
152            </intent-filter>
153        </receiver>
154    </application>
155
156</manifest>
