{"roots": ["my_115_app"], "packages": [{"name": "my_115_app", "version": "1.0.4", "dependencies": ["cached_network_image", "cupertino_icons", "dio", "dio_cache_interceptor", "dio_refresh", "flutter", "flutter_secure_storage", "flutter_system_action", "get_it", "media_kit", "media_kit_libs_video", "media_kit_video", "mutex", "photo_view", "provider", "screen_brightness", "shared_preferences", "talker", "talker_dio_logger", "talker_flutter"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_lints", "version": "6.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "mutex", "version": "3.1.0", "dependencies": []}, {"name": "flutter_system_action", "version": "1.0.4", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "screen_brightness", "version": "2.1.5", "dependencies": ["flutter", "screen_brightness_android", "screen_brightness_ios", "screen_brightness_macos", "screen_brightness_ohos", "screen_brightness_platform_interface", "screen_brightness_windows"]}, {"name": "media_kit_libs_video", "version": "1.0.6", "dependencies": ["media_kit_libs_android_video", "media_kit_libs_ios_video", "media_kit_libs_linux", "media_kit_libs_macos_video", "media_kit_libs_windows_video"]}, {"name": "media_kit_video", "version": "1.3.0", "dependencies": ["flutter", "media_kit", "plugin_platform_interface", "screen_brightness_android", "screen_brightness_platform_interface", "synchronized", "universal_platform", "volume_controller", "wakelock_plus", "web"]}, {"name": "media_kit", "version": "1.2.0", "dependencies": ["collection", "http", "image", "meta", "path", "safe_local_storage", "synchronized", "universal_platform", "uri_parser", "uuid", "web"]}, {"name": "photo_view", "version": "0.15.0", "dependencies": ["flutter"]}, {"name": "cached_network_image", "version": "3.4.1", "dependencies": ["cached_network_image_platform_interface", "cached_network_image_web", "flutter", "flutter_cache_manager", "octo_image"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "get_it", "version": "7.7.0", "dependencies": ["async", "collection", "meta"]}, {"name": "flutter_secure_storage", "version": "9.2.4", "dependencies": ["flutter", "flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_platform_interface", "flutter_secure_storage_web", "flutter_secure_storage_windows", "meta"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "talker_dio_logger", "version": "4.9.3", "dependencies": ["dio", "talker"]}, {"name": "talker_flutter", "version": "4.9.3", "dependencies": ["flutter", "group_button", "path_provider", "share_plus", "talker", "web"]}, {"name": "talker", "version": "4.9.3", "dependencies": ["talker_logger"]}, {"name": "dio_refresh", "version": "1.0.4", "dependencies": ["dio", "flutter", "jwt_decoder", "synchronized"]}, {"name": "dio_cache_interceptor", "version": "3.5.1", "dependencies": ["dio", "string_scanner", "uuid"]}, {"name": "dio", "version": "5.8.0+1", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "path"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "lints", "version": "6.0.0", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "screen_brightness_ohos", "version": "2.1.0", "dependencies": ["flutter", "screen_brightness_platform_interface"]}, {"name": "screen_brightness_windows", "version": "2.1.0", "dependencies": ["flutter", "screen_brightness_platform_interface"]}, {"name": "screen_brightness_macos", "version": "2.1.1", "dependencies": ["flutter", "screen_brightness_platform_interface"]}, {"name": "screen_brightness_ios", "version": "2.1.2", "dependencies": ["flutter", "screen_brightness_platform_interface"]}, {"name": "screen_brightness_android", "version": "2.1.2", "dependencies": ["flutter", "screen_brightness_platform_interface"]}, {"name": "screen_brightness_platform_interface", "version": "2.1.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "media_kit_libs_linux", "version": "1.2.1", "dependencies": ["flutter"]}, {"name": "media_kit_libs_windows_video", "version": "1.0.11", "dependencies": ["flutter"]}, {"name": "media_kit_libs_macos_video", "version": "1.1.4", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "media_kit_libs_ios_video", "version": "1.1.4", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "media_kit_libs_android_video", "version": "1.3.7", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "universal_platform", "version": "1.1.0", "dependencies": []}, {"name": "volume_controller", "version": "3.4.0", "dependencies": ["flutter"]}, {"name": "wakelock_plus", "version": "1.3.2", "dependencies": ["dbus", "flutter", "flutter_web_plugins", "meta", "package_info_plus", "wakelock_plus_platform_interface", "web", "win32"]}, {"name": "synchronized", "version": "3.4.0", "dependencies": []}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "uri_parser", "version": "3.0.0", "dependencies": ["path", "safe_local_storage"]}, {"name": "safe_local_storage", "version": "2.0.1", "dependencies": ["path", "synchronized"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "octo_image", "version": "2.1.0", "dependencies": ["flutter"]}, {"name": "flutter_cache_manager", "version": "3.4.1", "dependencies": ["clock", "collection", "file", "flutter", "http", "path", "path_provider", "rxdart", "sqflite", "uuid"]}, {"name": "cached_network_image_web", "version": "1.3.1", "dependencies": ["cached_network_image_platform_interface", "flutter", "flutter_cache_manager", "web"]}, {"name": "cached_network_image_platform_interface", "version": "4.1.1", "dependencies": ["flutter", "flutter_cache_manager"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "flutter_secure_storage_windows", "version": "3.1.2", "dependencies": ["ffi", "flutter", "flutter_secure_storage_platform_interface", "path", "path_provider", "win32"]}, {"name": "flutter_secure_storage_web", "version": "1.2.1", "dependencies": ["flutter", "flutter_secure_storage_platform_interface", "flutter_web_plugins", "js"]}, {"name": "flutter_secure_storage_platform_interface", "version": "1.1.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_secure_storage_macos", "version": "3.1.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "flutter_secure_storage_linux", "version": "1.2.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.10", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "share_plus", "version": "11.0.0", "dependencies": ["cross_file", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "mime", "share_plus_platform_interface", "url_launcher_linux", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows", "web", "win32"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "group_button", "version": "5.3.4", "dependencies": ["flutter"]}, {"name": "talker_logger", "version": "4.9.3", "dependencies": ["ansicolor", "web"]}, {"name": "jwt_decoder", "version": "2.0.1", "dependencies": []}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "package_info_plus", "version": "8.3.0", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "wakelock_plus_platform_interface", "version": "1.2.3", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "rxdart", "version": "0.28.0", "dependencies": []}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "share_plus_platform_interface", "version": "6.0.0", "dependencies": ["cross_file", "flutter", "meta", "mime", "path_provider", "plugin_platform_interface", "uuid"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "ansicolor", "version": "2.0.3", "dependencies": []}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "package_info_plus_platform_interface", "version": "3.2.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "posix", "version": "6.0.3", "dependencies": ["ffi", "meta", "path"]}, {"name": "sqflite_common", "version": "2.5.5", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}], "configVersion": 1}