androidx.appcompat.widget.ActionMenuView
io.flutter.plugins.pathprovider.PathProviderPlugin
dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin
dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin
io.flutter.plugin.platform.SingleViewPresentation
androidx.preference.DialogPreference
androidx.preference.TwoStatePreference
androidx.preference.internal.PreferenceImageView
go.Seq$RefTracker
com.aaassseee.screen_brightness_android.ScreenBrightnessAndroidPlugin
io.flutter.plugin.text.ProcessTextPlugin
com.example.my_115_app.MainActivity
com.alexmercerind.mediakitandroidhelper.MediaKitAndroidHelper
androidx.appcompat.widget.ViewStubCompat
androidx.appcompat.app.AlertController$RecycleListView
go.Seq$Proxy
androidx.appcompat.widget.FitWindowsFrameLayout
androidx.preference.PreferenceCategory
androidx.lifecycle.ReportFragment$LifecycleCallbacks
androidx.preference.ListPreference
androidx.lifecycle.ReportFragment
go.Seq
androidx.appcompat.widget.SearchView
dev.fluttercommunity.plus.share.SharePlusPlugin
androidx.recyclerview.widget.GridLayoutManager
androidx.recyclerview.widget.RecyclerView
kotlinx.coroutines.internal.StackTraceRecoveryKt
go.Universe$proxyerror
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements
com.kurenai7968.volume_controller.VolumeControllerPlugin
androidx.preference.DropDownPreference
androidx.appcompat.view.menu.ListMenuItemView
io.flutter.view.TextureRegistry$ImageTextureEntry
androidx.preference.PreferenceGroup
androidx.versionedparcelable.ParcelImpl
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback
dev.fluttercommunity.plus.share.SharePlusPendingIntent
androidx.appcompat.widget.ButtonBarLayout
androidx.preference.UnPressableLinearLayout
com.alexmercerind.media_kit_video.MediaKitVideoPlugin
io.flutter.view.FlutterCallbackInformation
androidx.appcompat.widget.ActionBarContainer
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference
go.error
androidx.appcompat.widget.ActionBarContextView
androidx.preference.SwitchPreference
go.Seq$GoRef
go.Seq$RefMap
androidx.window.extensions.core.util.function.Predicate
io.flutter.view.AccessibilityViewEmbedder
io.flutter.embedding.engine.FlutterJNI
androidx.profileinstaller.ProfileInstallReceiver
androidx.window.extensions.core.util.function.Function
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback
androidx.preference.CheckBoxPreference
androidx.annotation.Keep
androidx.lifecycle.ProcessLifecycleOwner$attach$1
androidx.core.graphics.drawable.IconCompatParcelizer
androidx.appcompat.widget.AlertDialogLayout
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer
androidx.startup.InitializationProvider
androidx.appcompat.widget.DialogTitle
go.Seq$GoObject
androidx.versionedparcelable.CustomVersionedParcelable
androidx.recyclerview.widget.LinearLayoutManager
androidx.appcompat.widget.Toolbar
com.it_nomads.fluttersecurestorage.FlutterSecureStoragePlugin
mobile.Mobile
kotlinx.coroutines.android.AndroidDispatcherFactory
io.flutter.plugins.GeneratedPluginRegistrant
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack
go.Universe
androidx.preference.EditTextPreference
androidx.preference.PreferenceScreen
go.Seq$GoRefQueue$1
androidx.preference.SwitchPreferenceCompat
io.flutter.view.TextureRegistry$SurfaceTextureEntry
androidx.core.graphics.drawable.IconCompat
androidx.appcompat.view.menu.ActionMenuItemView
androidx.core.app.CoreComponentFactory
android.support.v4.app.RemoteActionCompatParcelizer
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements
androidx.appcompat.widget.SearchView$SearchAutoComplete
androidx.recyclerview.widget.StaggeredGridLayoutManager
com.example.flutter_system_action.flutter_system_action.FlutterSystemActionPlugin
io.flutter.view.TextureRegistry$ImageConsumer
androidx.profileinstaller.ProfileInstallerInitializer
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements
dev.fluttercommunity.plus.share.ShareFileProvider
androidx.preference.SeekBarPreference
androidx.core.app.RemoteActionCompat
androidx.appcompat.widget.SwitchCompat
androidx.preference.MultiSelectListPreference
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin
go.Seq$GoRefQueue
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper
androidx.preference.Preference
io.flutter.embedding.engine.FlutterOverlaySurface
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements
androidx.lifecycle.ProcessLifecycleInitializer
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1
androidx.appcompat.view.menu.ExpandedMenuView
androidx.core.app.RemoteActionCompatParcelizer
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback
androidx.core.widget.NestedScrollView
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin
androidx.window.extensions.core.util.function.Consumer
androidx.appcompat.widget.FitWindowsLinearLayout
com.tekartik.sqflite.SqflitePlugin
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback
com.alexmercerind.media_kit_libs_android_video.MediaKitLibsAndroidVideoPlugin
kotlin.coroutines.jvm.internal.BaseContinuationImpl
go.mobile.gojni.R
androidx.appcompat.widget.ActionBarOverlayLayout
android.support.v4.graphics.drawable.IconCompatParcelizer
androidx.appcompat.widget.ActivityChooserView$InnerLayout
androidx.appcompat.widget.ContentFrameLayout
io.flutter.view.TextureRegistry$SurfaceProducer
go.Seq$Ref
io.flutter.view.TextureRegistry$GLTextureConsumer
go.Seq$Ref: int refcnt
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur
com.google.crypto.tink.proto.AesEaxKey: int version_
kotlinx.coroutines.internal.AtomicOp: java.lang.Object _consensus
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int keyId_
com.google.crypto.tink.proto.AesGcmKey: int KEY_VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.KmsAeadKey: int version_
com.google.crypto.tink.proto.KeyTypeEntry: boolean newKeyAllowed_
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_HASH_CODE
io.flutter.embedding.engine.FlutterJNI: float displayHeight
com.google.crypto.tink.proto.RegistryConfig: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: int HMAC_KEY_FORMAT_FIELD_NUMBER
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _parentHandle
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: int DEK_TEMPLATE_FIELD_NUMBER
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int deferredInsetTypes
com.google.crypto.tink.proto.AesSivKey: int KEY_VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.AesGcmSivKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.sync.SemaphoreImpl: long enqIdx
com.google.crypto.tink.proto.AesSivKeyFormat: int version_
androidx.appcompat.widget.Toolbar$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _prev
com.google.crypto.tink.proto.KeyTypeEntry: int NEW_KEY_ALLOWED_FIELD_NUMBER
com.google.crypto.tink.proto.ChaCha20Poly1305Key: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.crypto.tink.proto.AesSivKeyFormat: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.KmsAeadKey: com.google.crypto.tink.proto.KmsAeadKey DEFAULT_INSTANCE
com.google.crypto.tink.proto.KeyData: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.KeyTypeEntry: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: com.google.crypto.tink.proto.KmsEnvelopeAeadKey DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesGcmKeyFormat: int version_
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _next
com.google.crypto.tink.proto.KeyData: int VALUE_FIELD_NUMBER
androidx.datastore.preferences.PreferencesProto$Value: int FLOAT_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id
androidx.datastore.preferences.protobuf.GeneratedMessageLite: androidx.datastore.preferences.protobuf.UnknownFieldSetLite unknownFields
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.MapFieldLite preferences_
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting
com.google.crypto.tink.proto.KeysetInfo: com.google.crypto.tink.shaded.protobuf.Internal$ProtobufList keyInfo_
com.google.crypto.tink.proto.AesCtrKeyFormat: com.google.crypto.tink.proto.AesCtrKeyFormat DEFAULT_INSTANCE
kotlinx.coroutines.sync.MutexImpl: java.lang.Object owner
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Parser PARSER
com.google.crypto.tink.proto.HmacKey: int VERSION_FIELD_NUMBER
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.String TAG
io.flutter.embedding.engine.FlutterJNI: java.lang.String vmServiceUri
com.google.crypto.tink.proto.AesCtrKey: int PARAMS_FIELD_NUMBER
io.flutter.plugin.platform.SingleViewPresentation: android.content.Context outerContext
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: int VERSION_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.localization.LocalizationPlugin localizationPlugin
io.flutter.view.AccessibilityViewEmbedder: java.lang.String TAG
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: int AES_CTR_KEY_FORMAT_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrHmacAeadKey: com.google.crypto.tink.proto.AesCtrHmacAeadKey DEFAULT_INSTANCE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader lastReaderDequeuedFrom
kotlinx.coroutines.internal.LimitedDispatcher: int runningWorkers
com.google.crypto.tink.proto.AesCtrKeyFormat: int KEY_SIZE_FIELD_NUMBER
com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormat: int version_
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean needsSave
com.google.crypto.tink.proto.AesCmacKeyFormat: int KEY_SIZE_FIELD_NUMBER
com.google.crypto.tink.proto.AesEaxParams: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.UndispatchedCoroutine: boolean threadLocalIsSet
com.google.crypto.tink.proto.RegistryConfig: int ENTRY_FIELD_NUMBER
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MEMOIZED_SERIALIZED_SIZE_MASK
io.flutter.plugin.platform.SingleViewPresentation: java.lang.String TAG
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback callback
kotlinx.coroutines.channels.BufferedChannel: long sendersAndCloseStatus
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: java.util.Map defaultInstanceMap
com.google.crypto.tink.proto.KeyTemplate: java.lang.String typeUrl_
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean released
com.google.crypto.tink.proto.AesGcmKeyFormat: com.google.crypto.tink.proto.AesGcmKeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesEaxKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesCmacParams: int tagSize_
com.google.crypto.tink.proto.AesSivKeyFormat: int KEY_SIZE_FIELD_NUMBER
com.google.crypto.tink.proto.Keyset: int KEY_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id
com.google.crypto.tink.proto.AesGcmKey: int VERSION_FIELD_NUMBER
kotlinx.coroutines.sync.SemaphoreImpl: int _availablePermits
com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormat: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.AesCmacParams: com.google.crypto.tink.proto.AesCmacParams DEFAULT_INSTANCE
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
com.google.crypto.tink.proto.Keyset: int primaryKeyId_
com.google.crypto.tink.proto.KeysetInfo: com.google.crypto.tink.shaded.protobuf.Parser PARSER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedWidth
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean CLEANUP_ON_MEMORY_PRESSURE
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle
kotlinx.coroutines.EventLoopImplBase: int _isCompleted
com.google.crypto.tink.proto.XChaCha20Poly1305Key: com.google.crypto.tink.proto.XChaCha20Poly1305Key DEFAULT_INSTANCE
com.google.crypto.tink.proto.RegistryConfig: com.google.crypto.tink.shaded.protobuf.Internal$ProtobufList entry_
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: com.google.crypto.tink.shaded.protobuf.Parser PARSER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastScheduleTime
go.Seq$RefTracker: int REF_OFFSET
com.google.crypto.tink.proto.Keyset: int PRIMARY_KEY_ID_FIELD_NUMBER
com.google.crypto.tink.proto.AesSivKeyFormat: int keySize_
androidx.datastore.preferences.protobuf.AbstractMessageLite: int memoizedHashCode
androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem: android.os.Parcelable$Creator CREATOR
io.flutter.plugins.GeneratedPluginRegistrant: java.lang.String TAG
com.google.crypto.tink.proto.Keyset: com.google.crypto.tink.shaded.protobuf.Internal$ProtobufList key_
com.google.crypto.tink.proto.AesCmacParams: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.shaded.protobuf.AbstractMessageLite: int memoizedHashCode
com.google.crypto.tink.proto.Keyset$Key: int status_
com.google.crypto.tink.proto.RegistryConfig: java.lang.String configName_
com.google.crypto.tink.proto.AesGcmKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int memoizedSerializedSize
io.flutter.embedding.engine.FlutterJNI: android.os.Looper mainLooper
com.google.crypto.tink.proto.AesSivKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesCmacKey: com.google.crypto.tink.proto.AesCmacKey DEFAULT_INSTANCE
androidx.datastore.preferences.PreferencesProto$Value: int STRING_SET_FIELD_NUMBER
com.google.crypto.tink.proto.AesGcmSivKey: int VERSION_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: java.lang.String TAG
androidx.datastore.preferences.PreferencesProto$StringSet: int STRINGS_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean trimOnMemoryPressure
com.google.crypto.tink.proto.KeyTypeEntry: com.google.crypto.tink.proto.KeyTypeEntry DEFAULT_INSTANCE
com.google.crypto.tink.proto.EncryptedKeyset: com.google.crypto.tink.shaded.protobuf.ByteString encryptedKeyset_
com.google.crypto.tink.proto.KeyTypeEntry: java.lang.String primitiveName_
go.Universe$proxyerror: int refnum
kotlinx.coroutines.internal.ThreadSafeHeap: int _size
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface surface
com.google.crypto.tink.proto.AesSivKey: com.google.crypto.tink.proto.AesSivKey DEFAULT_INSTANCE
com.google.crypto.tink.proto.Keyset$Key: int KEY_DATA_FIELD_NUMBER
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_SERIALIZED_SIZE
com.google.crypto.tink.proto.AesCmacKeyFormat: int keySize_
com.google.crypto.tink.proto.HmacKey: int version_
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesCmacParams: int TAG_SIZE_FIELD_NUMBER
com.google.crypto.tink.proto.Keyset$Key: int outputPrefixType_
io.flutter.embedding.engine.FlutterJNI: float displayDensity
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedHeight
com.google.crypto.tink.proto.AesEaxKey: int PARAMS_FIELD_NUMBER
com.google.crypto.tink.proto.KmsAeadKey: int PARAMS_FIELD_NUMBER
go.Seq: java.util.logging.Logger log
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue
com.google.crypto.tink.proto.AesCmacKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object sendSegment
io.flutter.view.AccessibilityViewEmbedder: android.util.SparseArray flutterIdToOrigin
io.flutter.embedding.engine.FlutterOverlaySurface: int id
go.Seq$RefMap: int live
com.google.crypto.tink.proto.KeyTemplate: int TYPE_URL_FIELD_NUMBER
go.Seq$GoRefQueue$1: go.Seq$GoRefQueue this$0
com.google.crypto.tink.proto.HmacParams: int HASH_FIELD_NUMBER
com.google.crypto.tink.proto.AesCmacKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat params_
com.google.crypto.tink.proto.AesCtrKeyFormat: com.google.crypto.tink.proto.AesCtrParams params_
com.google.crypto.tink.proto.HmacParams: com.google.crypto.tink.proto.HmacParams DEFAULT_INSTANCE
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.Object lock
io.flutter.plugin.platform.SingleViewPresentation: android.widget.FrameLayout container
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Waiter waiters
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder
com.google.crypto.tink.proto.AesEaxKeyFormat: int KEY_SIZE_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: java.util.Set flutterUiDisplayListeners
kotlinx.coroutines.channels.BufferedChannel: long receivers
androidx.datastore.preferences.PreferencesProto$PreferenceMap: int PREFERENCES_FIELD_NUMBER
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.AccessibilityEventsDelegate accessibilityEventsDelegate
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
com.google.crypto.tink.proto.HmacKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesCtrHmacAeadKey: com.google.crypto.tink.proto.HmacKey hmacKey_
io.flutter.embedding.engine.FlutterJNI: boolean prefetchDefaultFontManagerCalled
com.google.crypto.tink.proto.AesCtrHmacAeadKey: com.google.crypto.tink.proto.AesCtrKey aesCtrKey_
com.google.crypto.tink.proto.AesEaxKey: com.google.crypto.tink.proto.AesEaxParams params_
go.Seq: go.Seq$GoRefQueue goRefQueue
kotlinx.coroutines.internal.Segment: int cleanedAndPointers
com.google.crypto.tink.proto.AesGcmSivKey: int version_
com.google.crypto.tink.proto.AesCmacKey: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.ChaCha20Poly1305Key: int version_
com.google.crypto.tink.proto.KmsAeadKeyFormat: java.lang.String keyUri_
com.google.crypto.tink.proto.AesGcmKey: int version_
com.google.crypto.tink.proto.AesGcmSivKeyFormat: com.google.crypto.tink.proto.AesGcmSivKeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.HmacKeyFormat: int keySize_
com.google.crypto.tink.proto.Keyset$Key: int keyId_
com.google.crypto.tink.proto.KeyData: com.google.crypto.tink.shaded.protobuf.ByteString value_
kotlinx.coroutines.sync.SemaphoreImpl: long deqIdx
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean ignoringFence
androidx.datastore.preferences.PreferencesProto$Value: int BOOLEAN_FIELD_NUMBER
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event$Companion Companion
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastQueueTime
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean notifiedDestroy
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: androidx.lifecycle.ProcessLifecycleOwner this$0
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: java.lang.Thread thread
com.google.crypto.tink.proto.HmacKeyFormat: int KEY_SIZE_FIELD_NUMBER
androidx.datastore.preferences.PreferencesProto$Value: int LONG_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.platform.PlatformViewsController2 platformViewsController2
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.PreferencesProto$PreferenceMap DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesEaxKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesCtrParams: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.HmacParams: int hash_
com.google.crypto.tink.proto.KeyTemplate: int outputPrefixType_
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask
com.google.crypto.tink.proto.KeyData: int keyMaterialType_
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback animationCallback
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next
com.google.crypto.tink.proto.HmacKeyFormat: com.google.crypto.tink.proto.HmacKeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int TYPE_URL_FIELD_NUMBER
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean released
com.google.crypto.tink.proto.AesGcmKeyFormat: int VERSION_FIELD_NUMBER
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed
com.google.crypto.tink.proto.AesSivKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
io.flutter.plugin.platform.SingleViewPresentation: boolean startFocused
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackName
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.Parser PARSER
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int OUTPUT_PREFIX_TYPE_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: boolean initCalled
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle lifecycle
go.Seq$RefMap: go.Seq$Ref[] objs
com.google.crypto.tink.proto.AesSivKeyFormat: com.google.crypto.tink.proto.AesSivKeyFormat DEFAULT_INSTANCE
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets lastWindowInsets
kotlinx.coroutines.internal.ResizableAtomicArray: java.util.concurrent.atomic.AtomicReferenceArray array
com.google.crypto.tink.proto.Keyset: com.google.crypto.tink.shaded.protobuf.Parser PARSER
androidx.datastore.preferences.PreferencesProto$Value: java.lang.Object value_
androidx.datastore.preferences.PreferencesProto$Value: int valueCase_
io.flutter.embedding.engine.FlutterJNI: java.lang.Long nativeShellHolderId
androidx.lifecycle.ReportFragment$LifecycleCallbacks: androidx.lifecycle.ReportFragment$LifecycleCallbacks$Companion Companion
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
kotlinx.coroutines.flow.StateFlowSlot: java.lang.Object _state
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$InsetsListener insetsListener
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState
com.google.crypto.tink.proto.KeyData: int TYPE_URL_FIELD_NUMBER
com.google.crypto.tink.proto.AesCmacKeyFormat: int PARAMS_FIELD_NUMBER
com.google.crypto.tink.proto.AesGcmSivKeyFormat: int version_
com.google.crypto.tink.proto.KeyTypeEntry: int TYPE_URL_FIELD_NUMBER
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object tail
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.dart.PlatformMessageHandler platformMessageHandler
com.google.crypto.tink.proto.Keyset$Key: int OUTPUT_PREFIX_TYPE_FIELD_NUMBER
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: int UNINITIALIZED_SERIALIZED_SIZE
com.google.crypto.tink.proto.RegistryConfig: int CONFIG_NAME_FIELD_NUMBER
com.google.crypto.tink.proto.KeysetInfo: int PRIMARY_KEY_ID_FIELD_NUMBER
go.Seq$GoRefQueue: java.util.Collection refs
com.google.crypto.tink.proto.KeysetInfo: com.google.crypto.tink.proto.KeysetInfo DEFAULT_INSTANCE
com.google.crypto.tink.proto.HmacParams: int TAG_SIZE_FIELD_NUMBER
androidx.datastore.preferences.PreferencesProto$Value: int INTEGER_FIELD_NUMBER
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: int memoizedSerializedSize
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Listener listeners
com.google.crypto.tink.proto.AesEaxKey: com.google.crypto.tink.proto.AesEaxKey DEFAULT_INSTANCE
go.Seq$RefTracker: java.util.IdentityHashMap javaRefs
go.Seq$Ref: java.lang.Object obj
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack
com.google.crypto.tink.proto.AesCtrHmacAeadKey: int AES_CTR_KEY_FIELD_NUMBER
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: int KEK_URI_FIELD_NUMBER
com.google.crypto.tink.proto.HmacKeyFormat: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.Keyset: com.google.crypto.tink.proto.Keyset DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesCtrHmacAeadKey: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture
com.google.crypto.tink.proto.KmsAeadKey: int VERSION_FIELD_NUMBER
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean attached
com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormat: com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.KmsAeadKeyFormat: int KEY_URI_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrKey: int version_
go.Seq$GoRef: int refnum
go.Seq$RefMap: int next
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastDequeueTime
com.google.crypto.tink.proto.AesEaxKeyFormat: com.google.crypto.tink.proto.AesEaxKeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesCmacKey: int version_
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean released
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: java.lang.Runnable onFrameConsumed
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object receiveSegment
com.google.crypto.tink.proto.AesCtrParams: int IV_SIZE_FIELD_NUMBER
com.google.crypto.tink.proto.AesEaxKeyFormat: int PARAMS_FIELD_NUMBER
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$AccessibilityDelegatingFrameLayout rootView
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
com.google.crypto.tink.proto.AesCtrHmacAeadKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesEaxKey: int KEY_VALUE_FIELD_NUMBER
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: int MUTABLE_FLAG_MASK
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object head
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback this$0
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean animating
kotlinx.coroutines.DefaultExecutor: int debugStatus
com.google.crypto.tink.proto.KmsAeadKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.KeyTemplate: com.google.crypto.tink.shaded.protobuf.Parser PARSER
io.flutter.embedding.engine.FlutterEngine: io.flutter.embedding.engine.FlutterJNI flutterJNI
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
com.google.crypto.tink.proto.ChaCha20Poly1305Key: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.AesGcmKeyFormat: int keySize_
com.google.crypto.tink.proto.AesGcmSivKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.KeysetInfo: int KEY_INFO_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: java.lang.String typeUrl_
com.google.crypto.tink.proto.Keyset$Key: int KEY_ID_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean VERBOSE_LOGS
io.flutter.embedding.engine.FlutterJNI: float refreshRateFPS
com.google.crypto.tink.proto.HmacKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
androidx.datastore.preferences.PreferencesProto$Value: int BYTES_FIELD_NUMBER
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
com.google.crypto.tink.proto.AesEaxParams: com.google.crypto.tink.proto.AesEaxParams DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesCtrParams: int ivSize_
com.google.crypto.tink.proto.KeyTypeEntry: int PRIMITIVE_NAME_FIELD_NUMBER
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation
com.google.crypto.tink.proto.AesCmacKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesCtrKeyFormat: int keySize_
androidx.appcompat.widget.SearchView$SavedState: android.os.Parcelable$Creator CREATOR
androidx.recyclerview.widget.RecyclerView$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean newFrameAvailable
com.google.crypto.tink.proto.XChaCha20Poly1305Key: int KEY_VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: com.google.crypto.tink.proto.HmacKeyFormat hmacKeyFormat_
com.google.crypto.tink.proto.AesEaxKeyFormat: com.google.crypto.tink.proto.AesEaxParams params_
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List mutators
androidx.lifecycle.ProcessLifecycleOwner$attach$1: androidx.lifecycle.ProcessLifecycleOwner this$0
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
com.google.crypto.tink.proto.XChaCha20Poly1305Key: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesGcmSivKey: int KEY_VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.HmacKeyFormat: int PARAMS_FIELD_NUMBER
io.flutter.view.AccessibilityViewEmbedder: java.util.Map originToFlutterId
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View view
com.google.crypto.tink.proto.EncryptedKeyset: int ENCRYPTED_KEYSET_FIELD_NUMBER
com.google.crypto.tink.proto.KmsAeadKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate accessibilityDelegate
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.protobuf.Parser PARSER
com.google.crypto.tink.proto.KeyTemplate: com.google.crypto.tink.shaded.protobuf.ByteString value_
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: com.google.crypto.tink.proto.AesCtrKeyFormat aesCtrKeyFormat_
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Internal$ProtobufList strings_
com.google.crypto.tink.proto.AesCtrHmacAeadKey: int version_
com.google.crypto.tink.proto.EncryptedKeyset: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesCtrKey: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.HmacParams: com.google.crypto.tink.shaded.protobuf.Parser PARSER
go.Seq$RefTracker: go.Seq$RefMap javaObjs
com.google.crypto.tink.proto.KmsAeadKey: com.google.crypto.tink.proto.KmsAeadKeyFormat params_
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: java.lang.String kekUri_
com.google.crypto.tink.proto.AesGcmSivKeyFormat: int keySize_
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object closeHandler
io.flutter.view.AccessibilityViewEmbedder: io.flutter.view.AccessibilityViewEmbedder$ReflectionAccessors reflectionAccessors
androidx.datastore.preferences.PreferencesProto$Value: int STRING_FIELD_NUMBER
com.google.crypto.tink.proto.KeyData: int KEY_MATERIAL_TYPE_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean createNewReader
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
kotlinx.coroutines.channels.BufferedChannel: long bufferEnd
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: int PARAMS_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrParams: com.google.crypto.tink.proto.AesCtrParams DEFAULT_INSTANCE
com.google.crypto.tink.proto.ChaCha20Poly1305KeyFormat: com.google.crypto.tink.proto.ChaCha20Poly1305KeyFormat DEFAULT_INSTANCE
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix finalMatrix
kotlinx.coroutines.CancellableContinuationImpl: int _decisionAndIndex
com.google.crypto.tink.proto.RegistryConfig: com.google.crypto.tink.proto.RegistryConfig DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesGcmSivKeyFormat: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.AesSivKey: int version_
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int status_
kotlinx.coroutines.android.HandlerContext: kotlinx.coroutines.android.HandlerContext _immediate
io.flutter.embedding.engine.FlutterJNI: float displayWidth
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int MAX_IMAGES
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackLibraryPath
androidx.concurrent.futures.AbstractResolvableFuture: java.lang.Object value
io.flutter.view.AccessibilityViewEmbedder: int nextFlutterId
com.google.crypto.tink.proto.ChaCha20Poly1305Key: com.google.crypto.tink.proto.ChaCha20Poly1305Key DEFAULT_INSTANCE
com.google.crypto.tink.proto.HmacKey: com.google.crypto.tink.proto.HmacKey DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesCtrHmacAeadKey: int HMAC_KEY_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean ignoringFence
com.google.crypto.tink.proto.AesEaxKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
go.Seq$RefTracker: int next
androidx.recyclerview.widget.LinearLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
androidx.customview.view.AbsSavedState: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.AesEaxKey: int VERSION_FIELD_NUMBER
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
io.flutter.view.AccessibilityViewEmbedder: java.util.Map embeddedViewToDisplayBounds
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int KEY_ID_FIELD_NUMBER
com.google.crypto.tink.proto.EncryptedKeyset: int KEYSET_INFO_FIELD_NUMBER
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: int MEMOIZED_SERIALIZED_SIZE_MASK
com.google.crypto.tink.proto.AesEaxKeyFormat: int keySize_
com.google.crypto.tink.proto.XChaCha20Poly1305Key: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.crypto.tink.proto.KeyTypeEntry: int keyManagerVersion_
kotlinx.coroutines.channels.BufferedChannel: long completedExpandBuffersAndPauseFlag
com.google.crypto.tink.proto.AesGcmKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesCmacKey: int KEY_VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.AesGcmKeyFormat: int KEY_SIZE_FIELD_NUMBER
com.google.crypto.tink.proto.HmacKeyFormat: int version_
io.flutter.view.AccessibilityViewEmbedder: android.view.View rootAccessibilityView
com.google.crypto.tink.proto.HmacKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesSivKey: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.ChaCha20Poly1305KeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int MAX_DEQUEUED_IMAGES
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: int version_
com.google.crypto.tink.proto.Keyset$Key: com.google.crypto.tink.proto.Keyset$Key DEFAULT_INSTANCE
com.google.crypto.tink.proto.Keyset$Key: int STATUS_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrKey: int KEY_VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.AesEaxParams: int IV_SIZE_FIELD_NUMBER
com.google.crypto.tink.proto.HmacKeyFormat: com.google.crypto.tink.proto.HmacParams params_
com.google.crypto.tink.proto.AesCmacKeyFormat: com.google.crypto.tink.proto.AesCmacParams params_
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.PreferencesProto$StringSet DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesGcmSivKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate asyncWaitForVsyncDelegate
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.ArrayDeque imageReaderQueue
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackClassName
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
com.google.crypto.tink.proto.EncryptedKeyset: com.google.crypto.tink.proto.KeysetInfo keysetInfo_
io.flutter.plugin.platform.SingleViewPresentation: android.view.View$OnFocusChangeListener focusChangeListener
com.google.crypto.tink.proto.KeyTypeEntry: int CATALOGUE_NAME_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrKey: com.google.crypto.tink.proto.AesCtrParams params_
io.flutter.embedding.engine.FlutterJNI: java.util.concurrent.locks.ReentrantReadWriteLock shellHolderLock
com.google.crypto.tink.proto.AesCmacKey: int PARAMS_FIELD_NUMBER
com.google.crypto.tink.proto.KeyData: com.google.crypto.tink.proto.KeyData DEFAULT_INSTANCE
go.Seq: go.Seq$RefTracker tracker
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.platform.PlatformViewsController platformViewsController
go.Seq: int NULL_REFNUM
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: com.google.crypto.tink.proto.KeyTemplate dekTemplate_
io.flutter.embedding.engine.FlutterJNI: boolean loadLibraryCalled
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.ArrayList lastDequeuedImage
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image image
com.google.crypto.tink.proto.AesCmacKeyFormat: com.google.crypto.tink.proto.AesCmacKeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.KeyData: java.lang.String typeUrl_
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: float finalOpacity
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState state
com.google.crypto.tink.proto.KeyTemplate: int VALUE_FIELD_NUMBER
androidx.core.widget.NestedScrollView$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int STATUS_FIELD_NUMBER
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer
com.google.crypto.tink.proto.KmsAeadKeyFormat: com.google.crypto.tink.proto.KmsAeadKeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.KeysetInfo: int primaryKeyId_
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MUTABLE_FLAG_MASK
com.google.crypto.tink.proto.AesEaxParams: int ivSize_
com.google.crypto.tink.proto.EncryptedKeyset: com.google.crypto.tink.proto.EncryptedKeyset DEFAULT_INSTANCE
androidx.datastore.preferences.PreferencesProto$Value: int DOUBLE_FIELD_NUMBER
com.google.crypto.tink.proto.ChaCha20Poly1305Key: int KEY_VALUE_FIELD_NUMBER
kotlinx.coroutines.JobSupport: java.lang.Object _state
kotlinx.coroutines.DispatchedCoroutine: int _decision
androidx.datastore.preferences.protobuf.GeneratedMessageLite: java.util.Map defaultInstanceMap
com.google.crypto.tink.proto.KeyTemplate: com.google.crypto.tink.proto.KeyTemplate DEFAULT_INSTANCE
io.flutter.plugin.platform.SingleViewPresentation: int viewId
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: androidx.concurrent.futures.AbstractResolvableFuture$Waiter next
kotlinx.coroutines.CompletedExceptionally: int _handled
com.google.crypto.tink.proto.AesCtrKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
go.Seq$RefMap: int[] keys
com.google.crypto.tink.proto.KeyTypeEntry: java.lang.String catalogueName_
com.google.crypto.tink.proto.HmacKey: int PARAMS_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: java.util.Set engineLifecycleListeners
com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.Keyset$Key: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.XChaCha20Poly1305Key: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: com.google.crypto.tink.proto.KeysetInfo$KeyInfo DEFAULT_INSTANCE
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated
com.google.crypto.tink.proto.AesCmacKey: com.google.crypto.tink.proto.AesCmacParams params_
go.Seq$Ref: int refnum
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object _closeCause
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.HashMap perImageReaders
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int outputPrefixType_
com.google.crypto.tink.proto.HmacKey: int KEY_VALUE_FIELD_NUMBER
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.PreferencesProto$Value DEFAULT_INSTANCE
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: com.google.crypto.tink.shaded.protobuf.UnknownFieldSetLite unknownFields
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager deferredComponentManager
com.google.crypto.tink.proto.KeyTemplate: int OUTPUT_PREFIX_TYPE_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrKey: com.google.crypto.tink.proto.AesCtrKey DEFAULT_INSTANCE
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
com.google.crypto.tink.proto.AesGcmKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.crypto.tink.proto.HmacParams: int tagSize_
kotlinx.coroutines.InvokeOnCancelling: int _invoked
com.google.crypto.tink.proto.AesSivKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
kotlinx.coroutines.CancelledContinuation: int _resumed
com.google.crypto.tink.proto.AesCtrKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.crypto.tink.proto.KeyTypeEntry: int KEY_MANAGER_VERSION_FIELD_NUMBER
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object bufferEndSegment
com.alexmercerind.mediakitandroidhelper.MediaKitAndroidHelper: android.content.Context applicationContext
com.google.crypto.tink.proto.AesGcmKey: com.google.crypto.tink.proto.AesGcmKey DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesGcmSivKey: com.google.crypto.tink.proto.AesGcmSivKey DEFAULT_INSTANCE
kotlinx.coroutines.flow.StateFlowImpl: java.lang.Object _state
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: int UNINITIALIZED_HASH_CODE
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.Keyset$Key: com.google.crypto.tink.proto.KeyData keyData_
io.flutter.embedding.engine.FlutterJNI: java.lang.String TAG
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List finalClippingPaths
com.google.crypto.tink.proto.KeyTypeEntry: java.lang.String typeUrl_
com.google.crypto.tink.proto.AesCtrKeyFormat: int PARAMS_FIELD_NUMBER
com.google.crypto.tink.proto.ChaCha20Poly1305Key: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesGcmSivKeyFormat: int KEY_SIZE_FIELD_NUMBER
com.google.crypto.tink.proto.XChaCha20Poly1305Key: int version_
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
com.google.crypto.tink.proto.HmacKey: com.google.crypto.tink.proto.HmacParams params_
androidx.recyclerview.widget.StaggeredGridLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
go.Seq: go.Seq$Ref nullRef
io.flutter.embedding.engine.FlutterJNI: void addIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix getFinalMatrix()
androidx.appcompat.widget.ActionBarOverlayLayout: int getActionBarHideOffset()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifier(int)
go.Seq$GoRefQueue$1: Seq$GoRefQueue$1(go.Seq$GoRefQueue)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: java.lang.CharSequence getContainerTitle(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean hasRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setHoverListener(androidx.appcompat.widget.MenuItemHoverListener)
androidx.core.widget.TextViewCompat$Api23Impl: android.graphics.PorterDuff$Mode getCompoundDrawableTintMode(android.widget.TextView)
androidx.appcompat.widget.LinearLayoutCompat: void setShowDividers(int)
io.flutter.view.TextureRegistry$SurfaceProducer: long id()
go.Seq$Ref: void inc()
io.flutter.embedding.engine.FlutterJNI: void setRefreshRateFPS(float)
androidx.appcompat.widget.ActionBarOverlayLayout: int getNestedScrollAxes()
androidx.appcompat.widget.SearchView: void setIconified(boolean)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getCollapseIcon()
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.FlutterJNI: void registerImageTexture(long,io.flutter.view.TextureRegistry$ImageConsumer,boolean)
androidx.appcompat.widget.SearchView: int getMaxWidth()
androidx.appcompat.widget.Toolbar: void setTitleMarginBottom(int)
androidx.appcompat.widget.ActionMenuView: int getWindowAnimations()
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean handlesCropAndRotation()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void maybeWaitOnFence(android.media.Image)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintList(android.view.MenuItem,android.content.res.ColorStateList)
androidx.appcompat.widget.ContentFrameLayout: void setAttachListener(androidx.appcompat.widget.ContentFrameLayout$OnAttachListener)
mobile.Mobile: long startProxyServer(long)
androidx.appcompat.widget.ActionBarContainer: void setTabContainer(androidx.appcompat.widget.ScrollingTabContainerView)
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQueryHint()
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetBottom(android.view.DisplayCutout)
com.google.crypto.tink.proto.OutputPrefixType: com.google.crypto.tink.proto.OutputPrefixType valueOf(java.lang.String)
com.alexmercerind.mediakitandroidhelper.MediaKitAndroidHelper: java.lang.String copyAssetToFilesDir(java.lang.String)
androidx.appcompat.widget.Toolbar: void setTitleMarginEnd(int)
androidx.appcompat.widget.AppCompatTextView: void setAutoSizeTextTypeWithDefaults(int)
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(int)
kotlinx.coroutines.android.AndroidExceptionPreHandler: void handleException(kotlin.coroutines.CoroutineContext,java.lang.Throwable)
androidx.appcompat.widget.ContentFrameLayout: ContentFrameLayout(android.content.Context,android.util.AttributeSet)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$400(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay[] values()
androidx.core.widget.NestedScrollView: float getTopFadingEdgeStrength()
androidx.appcompat.widget.SearchView: void setOnQueryTextFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.appcompat.view.menu.ActionMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation[] values()
go.Seq$GoObject: int incRefnum()
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowTitle(java.lang.CharSequence)
androidx.appcompat.widget.LinearLayoutCompat: void setVerticalGravity(int)
androidx.core.widget.TextViewCompat$Api23Impl: int getHyphenationFrequency(android.widget.TextView)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScroll(android.view.ViewParent,android.view.View,int,int,int,int)
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.core.content.res.FontResourcesParserCompat$Api21Impl: int getType(android.content.res.TypedArray,int)
io.flutter.embedding.engine.FlutterJNI: void destroyOverlaySurfaces()
io.flutter.view.TextureRegistry$ImageConsumer: android.media.Image acquireLatestImage()
androidx.appcompat.view.menu.ExpandedMenuView: int getWindowAnimations()
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.PlatformView,io.flutter.plugin.platform.AccessibilityEventsDelegate,int,android.view.View$OnFocusChangeListener)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnTrimMemoryListener(io.flutter.view.TextureRegistry$OnTrimMemoryListener)
com.google.crypto.tink.shaded.protobuf.ProtoSyntax: com.google.crypto.tink.shaded.protobuf.ProtoSyntax[] values()
androidx.appcompat.widget.ActionBarOverlayLayout: void setOverlayMode(boolean)
androidx.core.widget.NestedScrollView: void setSmoothScrollingEnabled(boolean)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax[] values()
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder valueOf(java.lang.String)
androidx.core.graphics.drawable.IconCompat: IconCompat()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointRegionalIndicator(int)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setContentDescription(android.view.MenuItem,java.lang.CharSequence)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
mobile.Mobile: Mobile()
io.flutter.embedding.android.FlutterImageView: android.view.Surface getSurface()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection valueOf(java.lang.String)
com.alexmercerind.mediakitandroidhelper.MediaKitAndroidHelper: void setApplicationContextNative(android.content.Context)
androidx.appcompat.widget.Toolbar: void setLogo(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: android.graphics.ColorFilter getColorFilter(android.graphics.drawable.Drawable)
io.flutter.embedding.android.FlutterView: void setWindowInfoListenerDisplayFeatures(androidx.window.layout.WindowLayoutInfo)
androidx.appcompat.view.menu.ListMenuItemView: ListMenuItemView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode valueOf(java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
androidx.core.view.ViewCompat$Api26Impl: void setTooltipText(android.view.View,java.lang.CharSequence)
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type valueOf(java.lang.String)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawableForDensity(android.content.res.Resources,int,int,android.content.res.Resources$Theme)
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Icon createWithAdaptiveBitmap(android.graphics.Bitmap)
io.flutter.embedding.engine.FlutterJNI: void nativeSetViewportMetrics(long,float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void startRearDisplayPresentationSession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: HiddenLifecycleReference(androidx.lifecycle.Lifecycle)
androidx.profileinstaller.ProfileInstallReceiver: ProfileInstallReceiver()
io.flutter.view.AccessibilityViewEmbedder: boolean onAccessibilityHoverEvent(int,android.view.MotionEvent)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getCollapseContentDescription()
androidx.window.extensions.core.util.function.Predicate: boolean test(java.lang.Object)
go.Seq$RefMap: void remove(int)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult valueOf(java.lang.String)
androidx.appcompat.view.menu.ActionMenuItemView: void setPopupCallback(androidx.appcompat.view.menu.ActionMenuItemView$PopupCallback)
androidx.appcompat.widget.SwitchCompat: int getSwitchMinWidth()
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection[] values()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarHideOffset(int)
go.Seq: java.util.logging.Logger access$000()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void finalize()
androidx.appcompat.widget.Toolbar: int getContentInsetEnd()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setContainerTitle(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
androidx.core.view.ViewCompat$Api28Impl: void setScreenReaderFocusable(android.view.View,boolean)
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetWidth()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
androidx.appcompat.widget.ActionBarOverlayLayout: void setLogo(int)
androidx.appcompat.widget.SwitchCompat: SwitchCompat(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SearchView: void setOnSearchClickListener(android.view.View$OnClickListener)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemColumnTitle(java.lang.Object)
androidx.appcompat.widget.LinearLayoutCompat: int getOrientation()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setDropDownBackgroundResource(int)
go.Seq: void trackGoRef(int,go.Seq$GoObject)
io.flutter.embedding.engine.FlutterJNI: void ensureNotAttachedToNative()
androidx.lifecycle.ProcessLifecycleInitializer: ProcessLifecycleInitializer()
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
io.flutter.embedding.engine.FlutterJNI: void endFrame2()
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$OnFlingListener getOnFlingListener()
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageEmptyResponseCallback(long,int)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerPadding()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Drawable createAdaptiveIconDrawable(android.graphics.drawable.Drawable,android.graphics.drawable.Drawable)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType[] values()
androidx.core.view.ViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getBackgroundTintMode(android.view.View)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void removeRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.security.crypto.EncryptedSharedPreferences$PrefValueEncryptionScheme: androidx.security.crypto.EncryptedSharedPreferences$PrefValueEncryptionScheme valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: FlutterRenderer$ImageReaderSurfaceProducer(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.appcompat.widget.SwitchCompat: void setTextOn(java.lang.CharSequence)
androidx.appcompat.widget.SearchView: int getPreferredHeight()
androidx.core.content.ContextCompat$Api28Impl: java.util.concurrent.Executor getMainExecutor(android.content.Context)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$102(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat computeSystemWindowInsets(android.view.View,androidx.core.view.WindowInsetsCompat,android.graphics.Rect)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateDisplayMetrics(long)
mobile.Mobile: void stopProxyServer()
androidx.core.view.ViewCompat$Api21Impl: android.content.res.ColorStateList getBackgroundTintList(android.view.View)
androidx.profileinstaller.ProfileInstallerInitializer$Choreographer16Impl: void postFrameCallback(java.lang.Runnable)
io.flutter.embedding.engine.FlutterJNI: boolean nativeGetIsSoftwareRenderingEnabled()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: boolean isTextSelectable(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.engine.FlutterJNI: void runBundleAndSnapshotFromLibrary(java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List,long)
go.Seq: void destroyRef(int)
androidx.core.view.ViewCompat$Api21Impl: float getTranslationZ(android.view.View)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityStopped(android.app.Activity)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void release()
androidx.core.graphics.drawable.IconCompat$Api30Impl: android.graphics.drawable.Icon createWithAdaptiveBitmapContentUri(android.net.Uri)
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin: LegacySharedPreferencesPlugin()
androidx.core.view.ViewCompat$Api26Impl: int getImportantForAutofill(android.view.View)
io.flutter.view.AccessibilityViewEmbedder: boolean requestSendAccessibilityEvent(android.view.View,android.view.View,android.view.accessibility.AccessibilityEvent)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.Toolbar: void setTitleTextColor(int)
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
io.flutter.embedding.engine.FlutterJNI: void nativeUnregisterTexture(long,long)
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageResponseCallback(long,int,java.nio.ByteBuffer,int)
mobile.Mobile: long getServerPort()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture,java.lang.Runnable)
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.PlatformView getView()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onImage(android.media.ImageReader,android.media.Image)
io.flutter.view.TextureRegistry$SurfaceProducer: boolean handlesCropAndRotation()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getNavigationIcon()
androidx.appcompat.widget.Toolbar: void setCollapseIcon(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setUniqueId(android.view.accessibility.AccessibilityNodeInfo,java.lang.String)
androidx.appcompat.view.menu.ActionMenuItemView: void setChecked(boolean)
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
androidx.appcompat.widget.Toolbar: Toolbar(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void pushImage(android.media.Image)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action,java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
com.alexmercerind.media_kit_video.MediaKitVideoPlugin: MediaKitVideoPlugin()
androidx.appcompat.widget.SwitchCompat: void setShowText(boolean)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: boolean canApplyTheme(android.graphics.drawable.Drawable)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetTop(android.view.DisplayCutout)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType[] values()
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetHeight()
io.flutter.embedding.engine.FlutterJNI: void nativeCleanupMessageData(long)
com.google.crypto.tink.KeyTemplate$OutputPrefixType: com.google.crypto.tink.KeyTemplate$OutputPrefixType valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api21Impl: float getElevation(android.view.View)
com.google.crypto.tink.shaded.protobuf.JavaType: com.google.crypto.tink.shaded.protobuf.JavaType valueOf(java.lang.String)
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarOverlayLayout: void setUiOptions(int)
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabled(boolean)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerDrawable(android.graphics.drawable.Drawable)
com.google.crypto.tink.proto.KeyData$KeyMaterialType: com.google.crypto.tink.proto.KeyData$KeyMaterialType valueOf(java.lang.String)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeNotifyLowMemoryWarning(long)
io.flutter.embedding.engine.FlutterJNI: void lambda$decodeImage$0(long,android.graphics.ImageDecoder,android.graphics.ImageDecoder$ImageInfo,android.graphics.ImageDecoder$Source)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets dispatchApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
androidx.core.content.ContextCompat$Api21Impl: java.io.File getNoBackupFilesDir(android.content.Context)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
androidx.core.view.ViewCompat$Api21Impl: void setTranslationZ(android.view.View,float)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceCreated(long,android.view.Surface)
io.flutter.embedding.engine.FlutterJNI: void detachFromNativeAndReleaseResources()
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int)
androidx.core.view.ViewCompat$Api26Impl: android.view.View keyboardNavigationClusterSearch(android.view.View,android.view.View,int)
io.flutter.embedding.engine.FlutterJNI: void onRenderingStopped()
androidx.appcompat.widget.SwitchCompat: void setSwitchMinWidth(int)
androidx.appcompat.widget.SwitchCompat: void setSwitchPadding(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
androidx.security.crypto.EncryptedSharedPreferences$EncryptedType: androidx.security.crypto.EncryptedSharedPreferences$EncryptedType[] values()
com.it_nomads.fluttersecurestorage.ciphers.KeyCipherAlgorithm: com.it_nomads.fluttersecurestorage.ciphers.KeyCipherAlgorithm[] values()
com.google.crypto.tink.config.internal.TinkFipsUtil$AlgorithmFipsCompatibility: com.google.crypto.tink.config.internal.TinkFipsUtil$AlgorithmFipsCompatibility[] values()
androidx.recyclerview.widget.RecyclerView: void setClipToPadding(boolean)
androidx.appcompat.widget.AppCompatTextView: android.view.textclassifier.TextClassifier getTextClassifier()
dev.fluttercommunity.plus.share.ShareFileProvider: ShareFileProvider()
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getTrackTintList()
androidx.recyclerview.widget.RecyclerView: androidx.core.view.NestedScrollingChildHelper getScrollingChildHelper()
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateRefreshRate(float)
androidx.appcompat.widget.SwitchCompat: void setTrackDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: void addEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase valueOf(java.lang.String)
io.flutter.view.AccessibilityViewEmbedder: void cacheVirtualIdMappings(android.view.View,int,int)
androidx.appcompat.widget.LinearLayoutCompat: int getBaselineAlignedChildIndex()
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintList(android.widget.TextView,android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api21Impl: boolean hasNestedScrollingParent(android.view.View)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: android.view.accessibility.AccessibilityNodeInfo$AccessibilityAction getActionScrollInDirection()
androidx.appcompat.widget.SwitchCompat: int getThumbTextPadding()
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchSemanticsAction(long,int,int,java.nio.ByteBuffer,int)
androidx.preference.PreferenceCategory: PreferenceCategory(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: int getContentInsetLeft()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: float getFinalOpacity()
androidx.appcompat.view.menu.ListMenuItemView: void setSubMenuArrowVisible(boolean)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI spawn(java.lang.String,java.lang.String,java.lang.String,java.util.List,long)
androidx.appcompat.widget.SearchView: void setOnCloseListener(androidx.appcompat.widget.SearchView$OnCloseListener)
androidx.appcompat.widget.ActionMenuView: int getPopupTheme()
io.flutter.plugin.platform.PlatformViewWrapper: void setLayoutParams(android.widget.FrameLayout$LayoutParams)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType[] values()
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Drawable loadDrawable(android.graphics.drawable.Icon,android.content.Context)
androidx.appcompat.widget.ViewStubCompat: void setInflatedId(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void release()
androidx.appcompat.view.menu.ListMenuItemView: void setForceShowIcon(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void disableFenceForTest()
androidx.appcompat.widget.ViewStubCompat: void setOnInflateListener(androidx.appcompat.widget.ViewStubCompat$OnInflateListener)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetStart()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintMode(android.view.MenuItem,android.graphics.PorterDuff$Mode)
androidx.core.widget.NestedScrollView: int getNestedScrollAxes()
io.flutter.view.TextureRegistry$SurfaceProducer: void setSize(int,int)
androidx.appcompat.view.menu.ActionMenuItemView: void setExpandedFormat(boolean)
androidx.core.view.ViewCompat$Api26Impl: boolean isImportantForAutofill(android.view.View)
androidx.core.view.MenuItemCompat$Api26Impl: android.content.res.ColorStateList getIconTintList(android.view.MenuItem)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
androidx.core.view.ViewCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.View)
io.flutter.embedding.engine.FlutterJNI: FlutterJNI()
io.flutter.plugins.pathprovider.PathProviderPlugin: PathProviderPlugin()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType valueOf(java.lang.String)
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
androidx.appcompat.widget.AppCompatTextView: int getLastBaselineToBottomHeight()
androidx.core.view.ViewCompat$Api28Impl: void addOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
androidx.appcompat.widget.AppCompatImageButton: void setImageURI(android.net.Uri)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.view.TextureRegistry$SurfaceProducer: int getHeight()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$MethodToInvoke: com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: int getTitleMarginEnd()
androidx.appcompat.widget.Toolbar: void setSubtitle(int)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context)
com.google.crypto.tink.shaded.protobuf.WireFormat$JavaType: com.google.crypto.tink.shaded.protobuf.WireFormat$JavaType valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabledInNative(boolean)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOn()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: android.util.DisplayMetrics getRearDisplayMetrics()
androidx.recyclerview.widget.RecyclerView: void setOnFlingListener(androidx.recyclerview.widget.RecyclerView$OnFlingListener)
io.flutter.embedding.android.FlutterView: void setVisibility(int)
androidx.appcompat.view.menu.ListMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.core.widget.TextViewCompat$Api23Impl: void setBreakStrategy(android.widget.TextView,int)
androidx.recyclerview.widget.RecyclerView: void setChildDrawingOrderCallback(androidx.recyclerview.widget.RecyclerView$ChildDrawingOrderCallback)
androidx.core.view.ViewCompat$Api21Impl: java.lang.String getTransitionName(android.view.View)
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getThumbTintList()
androidx.core.widget.TextViewCompat$Api23Impl: android.content.res.ColorStateList getCompoundDrawableTintList(android.widget.TextView)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.renderer.FlutterRenderer$ViewportMetrics getViewportMetrics()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate getCompatAccessibilityDelegate()
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
androidx.appcompat.view.menu.ListMenuItemView: void setGroupDividerEnabled(boolean)
androidx.appcompat.widget.LinearLayoutCompat: int getBaseline()
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void dispatchPlatformMessage(java.lang.String,java.nio.ByteBuffer,int,int)
io.flutter.embedding.engine.FlutterJNI: void destroyOverlaySurface2()
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.appcompat.widget.AppCompatImageView: void setImageBitmap(android.graphics.Bitmap)
io.flutter.embedding.engine.FlutterJNI: void attachToNative()
io.flutter.view.TextureRegistry$ImageTextureEntry: long id()
androidx.core.widget.TextViewCompat$Api23Impl: int getBreakStrategy(android.widget.TextView)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: ImeSyncDeferringInsetsCallback(android.view.View)
androidx.appcompat.widget.SwitchCompat: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image acquireLatestImage()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$502(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.WindowInsets)
io.flutter.embedding.engine.FlutterJNI: void onBeginFrame()
go.Universe: Universe()
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
androidx.core.view.DisplayCutoutCompat$Api28Impl: android.view.DisplayCutout createDisplayCutout(android.graphics.Rect,java.util.List)
androidx.recyclerview.widget.RecyclerView: int getMaxFlingVelocity()
io.flutter.embedding.engine.FlutterJNI: void updateRefreshRate()
io.flutter.embedding.engine.FlutterJNI: void onEndFrame()
androidx.appcompat.widget.SearchView: void setSuggestionsAdapter(androidx.cursoradapter.widget.CursorAdapter)
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMinor()
io.flutter.view.TextureRegistry$SurfaceProducer: android.view.Surface getSurface()
androidx.window.layout.util.ContextCompatHelperApi30: androidx.core.view.WindowInsetsCompat currentWindowInsets(android.content.Context)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: ImeSyncDeferringInsetsCallback$AnimationCallback(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.embedding.engine.FlutterJNI: long performNativeAttach(io.flutter.embedding.engine.FlutterJNI)
androidx.appcompat.widget.ActionBarOverlayLayout: void setShowingForActionMode(boolean)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceChanged(int,int)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection[] values()
androidx.appcompat.widget.Toolbar: int getPopupTheme()
androidx.core.view.ViewCompat$Api26Impl: boolean isKeyboardNavigationCluster(android.view.View)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap nativeGetBitmap(long)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceDestroyed(long)
androidx.core.view.ViewCompat$Api26Impl: void setImportantForAutofill(android.view.View,int)
androidx.appcompat.widget.AppCompatTextView: void setTextClassifier(android.view.textclassifier.TextClassifier)
mobile.Mobile: void _init()
androidx.core.view.ViewParentCompat$Api21Impl: boolean onStartNestedScroll(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
androidx.core.view.DisplayCutoutCompat$Api28Impl: java.util.List getBoundingRects(android.view.DisplayCutout)
androidx.core.view.ViewCompat$Api21Impl: float getZ(android.view.View)
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintMode(android.widget.ImageView,android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
androidx.appcompat.widget.ViewStubCompat: void setLayoutResource(int)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$ItemAnimator getItemAnimator()
androidx.appcompat.widget.LinearLayoutCompat: void setHorizontalGravity(int)
androidx.recyclerview.widget.LinearLayoutManager: LinearLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$LayoutManager getLayoutManager()
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action[] values()
io.flutter.embedding.android.FlutterView: void setDelegate(io.flutter.embedding.android.FlutterViewDelegate)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay valueOf(java.lang.String)
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodeParent(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.AppCompatTextView: void setPrecomputedText(androidx.core.text.PrecomputedTextCompat)
io.flutter.embedding.engine.FlutterJNI: android.view.SurfaceControl$Transaction createTransaction()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
androidx.core.view.VelocityTrackerCompat$Api34Impl: boolean isAxisSupported(android.view.VelocityTracker,int)
com.google.crypto.tink.shaded.protobuf.JavaType: com.google.crypto.tink.shaded.protobuf.JavaType[] values()
androidx.recyclerview.widget.RecyclerView: void setLayoutFrozen(boolean)
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setThreshold(int)
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory[] values()
go.Seq$Ref: Seq$Ref(int,java.lang.Object)
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState[] values()
androidx.appcompat.widget.ActionMenuView: android.view.Menu getMenu()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.core.view.ViewCompat$Api30Impl: boolean isImportantForContentCapture(android.view.View)
androidx.core.view.ViewCompat$Api23Impl: int getScrollIndicators(android.view.View)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.Object createRangeInfo(int,float,float,float)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap getBitmap()
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$MethodToInvoke: com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
io.flutter.embedding.engine.FlutterJNI: void requestDartDeferredLibrary(int)
androidx.appcompat.widget.SearchView$SearchAutoComplete: int getSearchViewTextMinWidthDp()
androidx.preference.ListPreference: ListPreference(android.content.Context,android.util.AttributeSet)
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: android.content.Context getPresentationContext()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
com.alexmercerind.media_kit_libs_android_video.MediaKitLibsAndroidVideoPlugin: MediaKitLibsAndroidVideoPlugin()
mobile.Mobile: void touch()
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMaximumFlingVelocity(android.view.ViewConfiguration,int,int,int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.recyclerview.widget.RecyclerView: void setLayoutManager(androidx.recyclerview.widget.RecyclerView$LayoutManager)
androidx.core.widget.EdgeEffectCompat$Api31Impl: android.widget.EdgeEffect create(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType[] values()
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: int getContentInsetStartWithNavigation()
io.flutter.embedding.engine.FlutterJNI: void loadDartDeferredLibrary(int,java.lang.String[])
androidx.appcompat.widget.SwitchCompat: int getSwitchPadding()
androidx.appcompat.widget.SwitchCompat: void setThumbResource(int)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getSubtitle()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setTooltipText(android.view.MenuItem,java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: boolean nativeIsSurfaceControlEnabled(long)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
go.Universe: void touch()
androidx.appcompat.widget.ButtonBarLayout: void setStacked(boolean)
androidx.appcompat.widget.ActionMenuView: void setOverflowReserved(boolean)
io.flutter.embedding.engine.FlutterJNI: void loadLibrary(android.content.Context)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.appcompat.widget.SearchView: void setSubmitButtonEnabled(boolean)
androidx.appcompat.widget.Toolbar: int getTitleMarginStart()
androidx.core.widget.NestedScrollView: void setFillViewport(boolean)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRect(int,int,int,int)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
com.google.crypto.tink.proto.HashType: com.google.crypto.tink.proto.HashType[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id()
androidx.preference.DropDownPreference: DropDownPreference(android.content.Context,android.util.AttributeSet)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$RecycledViewPool getRecycledViewPool()
androidx.appcompat.widget.Toolbar: void setTitleMarginTop(int)
androidx.appcompat.widget.SwitchCompat: void setThumbPosition(float)
io.flutter.embedding.engine.FlutterJNI: void nativeRunBundleAndSnapshotFromLibrary(long,java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List,long)
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
com.tekartik.sqflite.SqflitePlugin: SqflitePlugin()
androidx.appcompat.widget.LinearLayoutCompat: void setMeasureWithLargestChildEnabled(boolean)
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
io.flutter.embedding.android.FlutterView: io.flutter.embedding.android.FlutterImageView getCurrentImageSurface()
androidx.appcompat.widget.ActionBarContextView: void setContentHeight(int)
kotlin.random.Random: Random()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPaused(android.app.Activity)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: android.graphics.Matrix getPlatformViewMatrix()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getTitle()
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportImageTintMode()
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState valueOf(java.lang.String)
io.flutter.embedding.android.FlutterView: io.flutter.plugin.common.BinaryMessenger getBinaryMessenger()
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType[] values()
go.Seq$GoRefQueue$1: void run()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetRight()
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintList(android.widget.ImageView,android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api26Impl: boolean restoreDefaultFocus(android.view.View)
androidx.appcompat.widget.Toolbar: void setTitleTextColor(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI nativeSpawn(long,java.lang.String,java.lang.String,java.lang.String,java.util.List,long)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void maybeWaitOnFence(android.media.Image)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifier(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int access$200(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
go.Seq: void touch()
go.Seq$RefMap: Seq$RefMap()
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessageResponse(int,java.nio.ByteBuffer)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void addRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(java.lang.CharSequence)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage dequeueImage()
androidx.appcompat.widget.ViewStubCompat: int getLayoutResource()
androidx.preference.CheckBoxPreference: CheckBoxPreference(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getFinalClippingPaths()
androidx.core.view.ViewCompat$Api30Impl: int getImportantForContentCapture(android.view.View)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: FlutterMutatorsStack()
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(int)
androidx.appcompat.widget.AlertDialogLayout: AlertDialogLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarContextView: void setVisibility(int)
go.Universe: void _init()
androidx.appcompat.widget.SearchView: void setQuery(java.lang.CharSequence)
androidx.appcompat.widget.ActionMenuView: android.graphics.drawable.Drawable getOverflowIcon()
androidx.appcompat.widget.ActionBarContainer: ActionBarContainer(android.content.Context,android.util.AttributeSet)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: void setLastBaselineToBottomHeight(int)
androidx.appcompat.app.AlertController$RecycleListView: AlertController$RecycleListView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SearchView: int getImeOptions()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
androidx.recyclerview.widget.RecyclerView: int getItemDecorationCount()
io.flutter.embedding.engine.FlutterJNI: void nativeSetSemanticsEnabled(long,boolean)
androidx.appcompat.widget.SwitchCompat: void setTextOff(java.lang.CharSequence)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$100(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap decodeImage(java.nio.ByteBuffer,long)
io.flutter.embedding.engine.FlutterJNI: void setLocalizationPlugin(io.flutter.plugin.localization.LocalizationPlugin)
androidx.appcompat.widget.SwitchCompat: void setSplitTrack(boolean)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerWidth()
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getType(java.lang.Object)
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintList(android.view.View,android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
androidx.appcompat.widget.SwitchCompat: void setThumbTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.ViewCompat$Api26Impl: void setKeyboardNavigationCluster(android.view.View,boolean)
androidx.appcompat.widget.Toolbar: int getTitleMarginTop()
androidx.recyclerview.widget.RecyclerView: void setNestedScrollingEnabled(boolean)
androidx.security.crypto.EncryptedSharedPreferences$PrefValueEncryptionScheme: androidx.security.crypto.EncryptedSharedPreferences$PrefValueEncryptionScheme[] values()
androidx.appcompat.widget.LinearLayoutCompat: void setWeightSum(float)
io.flutter.embedding.engine.FlutterJNI: void unregisterTexture(long)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void removeRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: ProcessLifecycleOwner$attach$1$onActivityPreCreated$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.appcompat.view.menu.ListMenuItemView: void setChecked(boolean)
go.Universe$proxyerror: java.lang.String getMessage()
androidx.recyclerview.widget.RecyclerView: void setAdapter(androidx.recyclerview.widget.RecyclerView$Adapter)
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag[] values()
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
androidx.appcompat.widget.LinearLayoutCompat: float getWeightSum()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointVariantSelector(int)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getObservatoryUri()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMinTextSize()
androidx.core.widget.TextViewCompat$Api28Impl: android.text.PrecomputedText$Params getTextMetricsParams(android.widget.TextView)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetLeft()
androidx.appcompat.widget.LinearLayoutCompat: void setGravity(int)
mobile.Mobile: java.lang.String getProxyURL(java.lang.String)
go.Seq$GoRefQueue: java.util.Collection access$200(go.Seq$GoRefQueue)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo getRootNode(android.view.View,int,android.graphics.Rect)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature[] values()
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeaturesInNative(int)
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float onPullDistance(android.widget.EdgeEffect,float,float)
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(int)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMajor()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreFling(android.view.View,float,float)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getTooltipText(android.view.MenuItem)
io.flutter.embedding.engine.FlutterJNI: void notifyLowMemoryWarning()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$402(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.View)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getTitle()
androidx.appcompat.widget.ActionBarContainer: void setPrimaryBackground(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeScheduleFrame(long)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.LinearLayoutCompat: int getVirtualChildCount()
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getOrCreatePerImageReader(android.media.ImageReader)
io.flutter.embedding.engine.FlutterJNI: void nativeOnVsync(long,long,long)
androidx.appcompat.widget.LinearLayoutCompat: void setOrientation(int)
androidx.appcompat.widget.ActionBarContextView: void setCustomView(android.view.View)
go.Seq$GoRefQueue: void track(int,go.Seq$GoObject)
androidx.appcompat.widget.ActionBarOverlayLayout: ActionBarOverlayLayout(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void updateCustomAccessibilityActions(java.nio.ByteBuffer,java.lang.String[])
go.Seq$RefTracker: go.Seq$Ref get(int)
androidx.appcompat.widget.ViewStubCompat: void setLayoutInflater(android.view.LayoutInflater)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemRowTitle(java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode valueOf(java.lang.String)
androidx.security.crypto.EncryptedSharedPreferences$PrefKeyEncryptionScheme: androidx.security.crypto.EncryptedSharedPreferences$PrefKeyEncryptionScheme[] values()
androidx.core.view.ViewCompat$Api26Impl: android.view.autofill.AutofillId getAutofillId(android.view.View)
dev.fluttercommunity.plus.share.SharePlusPendingIntent: SharePlusPendingIntent()
androidx.appcompat.widget.Toolbar: void setLogoDescription(int)
androidx.appcompat.widget.ActionBarContextView: void setTitleOptional(boolean)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void remove()
androidx.appcompat.widget.AppCompatImageButton: void setImageBitmap(android.graphics.Bitmap)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void updateTexImage()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void finalize()
io.flutter.view.AccessibilityViewEmbedder: void copyAccessibilityFields(android.view.accessibility.AccessibilityNodeInfo,android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRRect(int,int,int,int,float[])
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType valueOf(java.lang.String)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostStarted(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void scheduleFrame()
androidx.appcompat.widget.LinearLayoutCompat: android.graphics.drawable.Drawable getDividerDrawable()
androidx.appcompat.widget.SwitchCompat: boolean getTargetCheckedState()
androidx.appcompat.widget.AppCompatTextView: void setBackgroundResource(int)
go.Seq$RefTracker: void dec(int)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportImageTintList()
androidx.appcompat.widget.Toolbar: android.view.Menu getMenu()
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintList(android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getVMServiceUri()
androidx.core.widget.NestedScrollView: int getScrollRange()
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
androidx.core.view.ViewCompat$Api20Impl: void requestApplyInsets(android.view.View)
androidx.appcompat.widget.AppCompatTextView: void setTextMetricsParamsCompat(androidx.core.text.PrecomputedTextCompat$Params)
androidx.preference.SeekBarPreference: SeekBarPreference(android.content.Context,android.util.AttributeSet)
io.flutter.view.TextureRegistry$ImageTextureEntry: void pushImage(android.media.Image)
io.flutter.view.FlutterCallbackInformation: io.flutter.view.FlutterCallbackInformation lookupCallbackInformation(long)
androidx.appcompat.widget.LinearLayoutCompat: int getShowDividers()
androidx.appcompat.widget.Toolbar: int getContentInsetRight()
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledVerticalScrollFactor(android.view.ViewConfiguration)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getParent(android.view.accessibility.AccessibilityNodeInfo,int)
androidx.appcompat.widget.Toolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
androidx.core.graphics.drawable.IconCompat$Api28Impl: java.lang.String getResPackage(java.lang.Object)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$302(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer,boolean)
dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin: PackageInfoPlugin()
com.google.crypto.tink.shaded.protobuf.Writer$FieldOrder: com.google.crypto.tink.shaded.protobuf.Writer$FieldOrder[] values()
com.google.crypto.tink.KeyTemplate$OutputPrefixType: com.google.crypto.tink.KeyTemplate$OutputPrefixType[] values()
go.Universe$proxyerror: int incRefnum()
go.Universe$proxyerror: java.lang.String error()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmoji(int)
io.flutter.view.TextureRegistry$SurfaceProducer: void scheduleFrame()
io.flutter.embedding.engine.FlutterJNI: void onDisplayPlatformView(int,int,int,int,int,int,int,io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack)
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
go.Seq$RefMap: go.Seq$Ref get(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setQueryFromAppProcessEnabled(android.view.accessibility.AccessibilityNodeInfo,android.view.View,boolean)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterOverlaySurface createOverlaySurface2()
io.flutter.embedding.engine.FlutterOverlaySurface: FlutterOverlaySurface(int,android.view.Surface)
io.flutter.embedding.android.FlutterImageView: android.media.ImageReader getImageReader()
androidx.security.crypto.MasterKey$Builder$Api23Impl$Api30Impl: void setUserAuthenticationParameters(android.security.keystore.KeyGenParameterSpec$Builder,int,int)
androidx.core.view.ViewCompat$Api28Impl: void removeOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void install()
androidx.profileinstaller.ProfileVerifier$Api33Impl: android.content.pm.PackageInfo getPackageInfo(android.content.pm.PackageManager,android.content.Context)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
io.flutter.view.AccessibilityViewEmbedder: java.lang.Integer getRecordFlutterId(android.view.View,android.view.accessibility.AccessibilityRecord)
androidx.security.crypto.MasterKey$Builder$Api23Impl$Api28Impl: void setIsStrongBoxBacked(android.security.keystore.KeyGenParameterSpec$Builder)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides[] values()
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.appcompat.widget.AppCompatImageView: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowCallback(android.view.Window$Callback)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onPrepare(android.view.WindowInsetsAnimation)
androidx.appcompat.widget.ActionMenuView: void setPopupTheme(int)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
androidx.appcompat.widget.SwitchCompat: void setChecked(boolean)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeatures(int)
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation valueOf(java.lang.String)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
go.Universe$proxyerror: Universe$proxyerror(int)
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType[] values()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow[] values()
androidx.appcompat.widget.FitWindowsFrameLayout: FitWindowsFrameLayout(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageResponseCallback(int,java.nio.ByteBuffer,int)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
androidx.security.crypto.EncryptedSharedPreferences$PrefKeyEncryptionScheme: androidx.security.crypto.EncryptedSharedPreferences$PrefKeyEncryptionScheme valueOf(java.lang.String)
go.Seq$RefTracker: Seq$RefTracker()
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.core.widget.ImageViewCompat$Api21Impl: android.content.res.ColorStateList getImageTintList(android.widget.ImageView)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void endRearDisplaySession()
androidx.core.view.ViewCompat$Api28Impl: java.lang.Object requireViewById(android.view.View,int)
androidx.appcompat.widget.DialogTitle: DialogTitle(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,int,java.nio.ByteBuffer,int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onTrimMemory(int)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintList(android.graphics.drawable.Drawable,android.content.res.ColorStateList)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void applyTheme(android.graphics.drawable.Drawable,android.content.res.Resources$Theme)
androidx.core.view.ViewCompat$Api28Impl: boolean isScreenReaderFocusable(android.view.View)
androidx.appcompat.widget.Toolbar: void setCollapseIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SearchView: void setMaxWidth(int)
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.SearchView: int getSuggestionRowLayout()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.recyclerview.widget.RecyclerView: int getBaseline()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMaxTextSize()
io.flutter.plugins.GeneratedPluginRegistrant: GeneratedPluginRegistrant()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.view.Surface getSurface()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void getBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void detachFromGLContext()
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getThumbTintMode()
androidx.appcompat.widget.Toolbar: void setNavigationOnClickListener(android.view.View$OnClickListener)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type[] values()
go.Seq: void decRef(int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
io.flutter.embedding.engine.FlutterJNI: void onFirstFrame()
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void onVsync(long,long,long)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
io.flutter.embedding.engine.FlutterJNI: void updateJavaAssetManager(android.content.res.AssetManager,java.lang.String)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getOverflowIcon()
androidx.appcompat.widget.SearchView: void setSearchableInfo(android.app.SearchableInfo)
androidx.core.view.ViewCompat$Api26Impl: void setNextClusterForwardId(android.view.View,int)
io.flutter.embedding.engine.FlutterJNI: void nativeMarkTextureFrameAvailable(long,long)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQuery()
androidx.core.widget.NestedScrollView: NestedScrollView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SwitchCompat: void setThumbTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.ActionBarContextView: void setTitle(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void markTextureFrameAvailable(long)
io.flutter.embedding.engine.FlutterJNI: void dispatchPointerDataPacket(java.nio.ByteBuffer,int)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(int)
androidx.appcompat.widget.AppCompatImageButton: void setImageResource(int)
io.flutter.embedding.android.FlutterSurfaceView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.recyclerview.widget.RecyclerView: int getMinFlingVelocity()
androidx.core.view.ViewCompat$Api26Impl: void addKeyboardNavigationClusters(android.view.View,java.util.Collection,int)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityCreated(android.app.Activity,android.os.Bundle)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: double deltaMillis(long)
androidx.appcompat.view.menu.ActionMenuItemView: void setCheckable(boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat valueOf(java.lang.String)
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.SwitchCompat: void setThumbDrawable(android.graphics.drawable.Drawable)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: long id()
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundResource(int)
io.flutter.plugins.GeneratedPluginRegistrant: void registerWith(io.flutter.embedding.engine.FlutterEngine)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerPadding(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnFrameConsumedListener(io.flutter.view.TextureRegistry$OnFrameConsumedListener)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
androidx.preference.EditTextPreference: EditTextPreference(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void updateSemantics(java.nio.ByteBuffer,java.lang.String[],java.nio.ByteBuffer[])
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getTrackDrawable()
io.flutter.embedding.engine.FlutterJNI: void onSurfaceDestroyed()
androidx.preference.TwoStatePreference: TwoStatePreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SwitchCompat: void setTrackTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAligned(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onEnd(android.view.WindowInsetsAnimation)
androidx.recyclerview.widget.RecyclerView: int getScrollState()
androidx.recyclerview.widget.RecyclerView: boolean getClipToPadding()
io.flutter.view.AccessibilityViewEmbedder: AccessibilityViewEmbedder(android.view.View,int)
io.flutter.view.AccessibilityViewEmbedder: void addChildrenToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.Toolbar: void setLogoDescription(java.lang.CharSequence)
dev.fluttercommunity.plus.share.SharePlusPlugin: SharePlusPlugin()
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterImageTexture(long,long,java.lang.ref.WeakReference,boolean)
io.flutter.plugin.platform.PlatformViewWrapper: void setTouchProcessor(io.flutter.embedding.android.AndroidTouchProcessor)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.net.Uri getUri(java.lang.Object)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
go.Seq$RefTracker: void incRefnum(int)
androidx.appcompat.widget.SearchView: void setInputType(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmoji(int)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceCreated(android.view.Surface)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind[] values()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode[] values()
androidx.appcompat.widget.SearchView: androidx.cursoradapter.widget.CursorAdapter getSuggestionsAdapter()
androidx.appcompat.widget.ActionBarContainer: void setStackedBackground(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
go.Seq: void incRefnum(int)
io.flutter.embedding.engine.FlutterJNI: float getScaledFontSize(float,int)
androidx.appcompat.view.menu.ExpandedMenuView: ExpandedMenuView(android.content.Context,android.util.AttributeSet)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View$OnApplyWindowInsetsListener getInsetsListener()
androidx.appcompat.widget.SwitchCompat: void setTrackResource(int)
androidx.appcompat.widget.Toolbar: void setTitleMarginStart(int)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getUniqueId(android.view.accessibility.AccessibilityNodeInfo)
androidx.security.crypto.MasterKey$KeyScheme: androidx.security.crypto.MasterKey$KeyScheme valueOf(java.lang.String)
androidx.appcompat.widget.FitWindowsLinearLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
io.flutter.embedding.engine.FlutterJNI: void nativeDeferredComponentInstallFailure(int,java.lang.String,boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
androidx.appcompat.widget.ActionBarContextView: int getContentHeight()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
io.flutter.view.TextureRegistry$SurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
androidx.appcompat.widget.SwitchCompat: void setSwitchTypeface(android.graphics.Typeface)
io.flutter.embedding.android.FlutterTextureView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void endRearDisplayPresentationSession()
go.Seq: void incGoRef(int,go.Seq$GoObject)
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportImageTintList()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean isAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo)
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
kotlinx.coroutines.android.AndroidDispatcherFactory: kotlinx.coroutines.MainCoroutineDispatcher createDispatcher(java.util.List)
androidx.appcompat.widget.ActionMenuView: void setOverflowIcon(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: void setPlatformMessageHandler(io.flutter.embedding.engine.dart.PlatformMessageHandler)
androidx.appcompat.widget.AppCompatTextView: int getFirstBaselineToTopHeight()
androidx.core.view.ViewParentCompat$Api21Impl: void onStopNestedScroll(android.view.ViewParent,android.view.View)
androidx.core.view.ViewCompat$Api21Impl: boolean isImportantForAccessibility(android.view.View)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedPreScroll(android.view.ViewParent,android.view.View,int,int,int[])
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void cleanup()
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.ActionBarContainer: void setSplitBackground(android.graphics.drawable.Drawable)
androidx.core.widget.TextViewCompat$Api24Impl: android.icu.text.DecimalFormatSymbols getInstance(java.util.Locale)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: androidx.window.extensions.area.ExtensionWindowAreaPresentation getRearDisplayPresentation()
go.Seq: int incGoObjectRef(go.Seq$GoObject)
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin: SharedPreferencesPlugin()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: long getMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.LinearLayoutCompat: int getGravity()
io.flutter.embedding.engine.FlutterJNI: void cleanupMessageData(long)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void waitOnFence(android.media.Image)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
androidx.lifecycle.ReportFragment: ReportFragment()
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
androidx.core.widget.NestedScrollView: void setNestedScrollingEnabled(boolean)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipPath(android.graphics.Path)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatTextView: void setLineHeight(int)
androidx.appcompat.view.menu.ListMenuItemView: void setTitle(java.lang.CharSequence)
androidx.core.widget.NestedScrollView: int getMaxScrollAmount()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType[] values()
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPointerDataPacket(long,java.nio.ByteBuffer,int)
androidx.recyclerview.widget.RecyclerView: void setHasFixedSize(boolean)
androidx.appcompat.widget.SearchView: void setOnSuggestionListener(androidx.appcompat.widget.SearchView$OnSuggestionListener)
androidx.appcompat.widget.SwitchCompat: int getThumbScrollRange()
androidx.core.view.ViewCompat$Api26Impl: int getNextClusterForwardId(android.view.View)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
androidx.appcompat.widget.SearchView: void setImeOptions(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState[] values()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void getTransformMatrix(float[])
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind valueOf(java.lang.String)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setSelector(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextView: androidx.core.text.PrecomputedTextCompat$Params getTextMetricsParamsCompat()
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
io.flutter.embedding.engine.FlutterJNI: void dispatchEmptyPlatformMessage(java.lang.String,int)
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int,int)
androidx.startup.InitializationProvider: InitializationProvider()
androidx.appcompat.widget.ActionMenuView: void setPresenter(androidx.appcompat.widget.ActionMenuPresenter)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setAlphabeticShortcut(android.view.MenuItem,char,int)
com.google.crypto.tink.shaded.protobuf.WireFormat$JavaType: com.google.crypto.tink.shaded.protobuf.WireFormat$JavaType[] values()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$Adapter getAdapter()
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledHorizontalScrollFactor(android.view.ViewConfiguration)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityDelegate(io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate)
androidx.appcompat.widget.ButtonBarLayout: void setAllowStacking(boolean)
androidx.appcompat.widget.SwitchCompat: void setTrackTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifierBase(int)
io.flutter.view.TextureRegistry$SurfaceProducer: void release()
androidx.appcompat.widget.SwitchCompat: void setThumbTextPadding(int)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintMode(android.view.View,android.graphics.PorterDuff$Mode)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetLeft(android.view.DisplayCutout)
go.error: java.lang.String error()
androidx.recyclerview.widget.RecyclerView: void setRecycledViewPool(androidx.recyclerview.widget.RecyclerView$RecycledViewPool)
androidx.core.view.ViewCompat$Api21Impl: boolean isNestedScrollingEnabled(android.view.View)
androidx.appcompat.widget.SearchView: void setOnQueryTextListener(androidx.appcompat.widget.SearchView$OnQueryTextListener)
com.alexmercerind.mediakitandroidhelper.MediaKitAndroidHelper: MediaKitAndroidHelper()
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType valueOf(java.lang.String)
androidx.appcompat.widget.SearchView: int getPreferredWidth()
com.google.crypto.tink.shaded.protobuf.ProtoSyntax: com.google.crypto.tink.shaded.protobuf.ProtoSyntax valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMajor()
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintMode(android.graphics.drawable.Drawable,android.graphics.PorterDuff$Mode)
io.flutter.embedding.android.FlutterView: android.view.accessibility.AccessibilityNodeProvider getAccessibilityNodeProvider()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: android.graphics.SurfaceTexture surfaceTexture()
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setImeVisibility(boolean)
androidx.core.widget.EdgeEffectCompat$Api21Impl: void onPull(android.widget.EdgeEffect,float,float)
mobile.Mobile: boolean isServerRunning()
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.core.widget.PopupWindowCompat$Api23Impl: int getWindowLayoutType(android.widget.PopupWindow)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
com.google.crypto.tink.config.internal.TinkFipsUtil$AlgorithmFipsCompatibility: com.google.crypto.tink.config.internal.TinkFipsUtil$AlgorithmFipsCompatibility valueOf(java.lang.String)
io.flutter.view.TextureRegistry$GLTextureConsumer: android.graphics.SurfaceTexture getSurfaceTexture()
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityHeading(android.view.View,boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getWidth()
androidx.core.view.ViewCompat$Api29Impl: void setSystemGestureExclusionRects(android.view.View,java.util.List)
go.Seq: Seq()
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceWindowChanged(android.view.Surface)
io.flutter.embedding.engine.FlutterJNI: io.flutter.view.FlutterCallbackInformation nativeLookupCallbackInformation(long)
androidx.core.view.ViewCompat$Api29Impl: void saveAttributeDataForStyleable(android.view.View,android.content.Context,int[],android.util.AttributeSet,android.content.res.TypedArray,int,int)
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessage(java.lang.String,java.nio.ByteBuffer,int,long)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
com.google.crypto.tink.shaded.protobuf.FieldType$Collection: com.google.crypto.tink.shaded.protobuf.FieldType$Collection valueOf(java.lang.String)
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.CharSequence castToCharSequence(android.text.PrecomputedText)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture)
com.alexmercerind.mediakitandroidhelper.MediaKitAndroidHelper: void setApplicationContextJava(android.content.Context)
androidx.core.graphics.Insets$Api29Impl: android.graphics.Insets of(int,int,int,int)
com.alexmercerind.mediakitandroidhelper.MediaKitAndroidHelper: int openFileDescriptorNative(java.lang.String)
com.google.crypto.tink.shaded.protobuf.FieldType: com.google.crypto.tink.shaded.protobuf.FieldType valueOf(java.lang.String)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedFling(android.view.ViewParent,android.view.View,float,float,boolean)
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeInit(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long,int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id()
io.flutter.embedding.android.FlutterTextureView: void setRenderSurface(android.view.Surface)
androidx.appcompat.widget.SearchView$SearchAutoComplete: SearchView$SearchAutoComplete(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void removeIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspotBounds(android.graphics.drawable.Drawable,int,int,int,int)
androidx.appcompat.widget.Toolbar: void setTitle(java.lang.CharSequence)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMinor()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void waitOnFence(android.media.Image)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getHeight()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
androidx.preference.internal.PreferenceImageView: int getMaxWidth()
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.DecorToolbar getWrapper()
com.it_nomads.fluttersecurestorage.FlutterSecureStoragePlugin: FlutterSecureStoragePlugin()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int pendingDequeuedImages()
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType valueOf(java.lang.String)
com.google.crypto.tink.shaded.protobuf.FieldType: com.google.crypto.tink.shaded.protobuf.FieldType[] values()
androidx.appcompat.widget.AppCompatTextView: int[] getAutoSizeTextAvailableSizes()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
com.google.crypto.tink.proto.HashType: com.google.crypto.tink.proto.HashType valueOf(java.lang.String)
go.Seq$GoRef: Seq$GoRef(int,go.Seq$GoObject,go.Seq$GoRefQueue)
androidx.core.view.MenuItemCompat$Api26Impl: int getNumericModifiers(android.view.MenuItem)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifierBase(int)
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodesTranslateBounds(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect,android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.ActionBarOverlayLayout: void setHideOnContentScrollEnabled(boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingRight()
androidx.appcompat.widget.ActionBarContextView: int getAnimatedVisibility()
androidx.appcompat.widget.Toolbar: android.widget.TextView getTitleTextView()
androidx.core.widget.TextViewCompat$Api28Impl: void setFirstBaselineToTopHeight(android.widget.TextView,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
io.flutter.plugin.platform.PlatformViewWrapper: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.security.crypto.MasterKey$KeyScheme: androidx.security.crypto.MasterKey$KeyScheme[] values()
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode[] values()
com.google.crypto.tink.shaded.protobuf.FieldType$Collection: com.google.crypto.tink.shaded.protobuf.FieldType$Collection[] values()
androidx.core.view.ViewCompat$Api26Impl: void setAutofillHints(android.view.View,java.lang.String[])
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getLogoDescription()
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.preference.UnPressableLinearLayout: UnPressableLinearLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarContainer: android.view.View getTabContainer()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void release()
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getResId(java.lang.Object)
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getMutators()
io.flutter.embedding.engine.FlutterJNI: void setPlatformViewsController(io.flutter.plugin.platform.PlatformViewsController)
io.flutter.embedding.engine.FlutterJNI: void asyncWaitForVsync(long)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityPaneTitle(android.view.View,java.lang.CharSequence)
androidx.core.view.ViewCompat$Api26Impl: boolean isFocusedByDefault(android.view.View)
io.flutter.embedding.engine.FlutterJNI: java.lang.String[] computePlatformResolvedLocale(java.lang.String[])
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void addRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void markDirty()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreScroll(android.view.View,int,int,int[],int[])
io.flutter.view.TextureRegistry$SurfaceLifecycle: io.flutter.view.TextureRegistry$SurfaceLifecycle valueOf(java.lang.String)
androidx.appcompat.view.menu.ActionMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
io.flutter.embedding.engine.FlutterJNI: void init(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long,int)
io.flutter.view.AccessibilityViewEmbedder: android.view.View platformViewOfNode(int)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode valueOf(java.lang.String)
androidx.appcompat.widget.ButtonBarLayout: ButtonBarLayout(android.content.Context,android.util.AttributeSet)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: void setStateDescription(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: void setTitle(int)
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
androidx.recyclerview.widget.RecyclerView: void setScrollingTouchSlop(int)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action valueOf(java.lang.String)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageView: void setImageURI(android.net.Uri)
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: FlutterRenderer$ImageTextureRegistryEntry(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.appcompat.widget.AppCompatTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.AccessibilityEventsDelegate,io.flutter.plugin.platform.SingleViewPresentation$PresentationState,android.view.View$OnFocusChangeListener,boolean)
androidx.tracing.TraceApi29Impl: boolean isEnabled()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundResource(int)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setNumericShortcut(android.view.MenuItem,char,int)
com.google.crypto.tink.proto.KeyData$KeyMaterialType: com.google.crypto.tink.proto.KeyData$KeyMaterialType[] values()
androidx.recyclerview.widget.RecyclerView: void setViewCacheExtension(androidx.recyclerview.widget.RecyclerView$ViewCacheExtension)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setSize(int,int)
androidx.recyclerview.widget.RecyclerView: long getNanoTime()
com.example.flutter_system_action.flutter_system_action.FlutterSystemActionPlugin: FlutterSystemActionPlugin()
androidx.core.view.ViewCompat$Api28Impl: void setAutofillId(android.view.View,androidx.core.view.autofill.AutofillIdCompat)
androidx.appcompat.widget.AppCompatTextView: void setFirstBaselineToTopHeight(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void scheduleFrame()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader29()
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int,int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getChild(android.view.accessibility.AccessibilityNodeInfo,int,int)
androidx.appcompat.widget.ActionBarContextView: ActionBarContextView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void nativeDestroy(long)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
androidx.core.widget.TextViewCompat$Api23Impl: void setHyphenationFrequency(android.widget.TextView,int)
androidx.recyclerview.widget.RecyclerView: void setOnScrollListener(androidx.recyclerview.widget.RecyclerView$OnScrollListener)
androidx.core.view.ViewCompat$Api26Impl: void setFocusedByDefault(android.view.View,boolean)
androidx.appcompat.widget.SwitchCompat: int getThumbOffset()
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getTrackTintMode()
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action)
androidx.appcompat.widget.Toolbar: void setSubtitle(java.lang.CharSequence)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
androidx.core.view.ViewCompat$Api29Impl: android.view.contentcapture.ContentCaptureSession getContentCaptureSession(android.view.View)
androidx.appcompat.widget.Toolbar: int getContentInsetStart()
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness[] values()
androidx.preference.internal.PreferenceImageView: int getMaxHeight()
io.flutter.embedding.engine.FlutterJNI: void setPlatformViewsController2(io.flutter.plugin.platform.PlatformViewsController2)
io.flutter.embedding.engine.FlutterJNI: void prefetchDefaultFontManager()
go.mobile.gojni.R: R()
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: int getLayoutDirection(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
go.Seq$Ref: int access$100(go.Seq$Ref)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedScroll(android.view.View,int,int,int,int,int[])
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
go.Seq$RefMap: void grow()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeTextType()
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.recyclerview.widget.RecyclerView: void setPreserveFocusAfterLayout(boolean)
io.flutter.plugin.platform.PlatformViewWrapper: android.view.ViewTreeObserver$OnGlobalFocusChangeListener getActiveFocusListener()
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
go.Seq: void setContext(java.lang.Object)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback access$200(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintMode(android.widget.TextView,android.graphics.PorterDuff$Mode)
com.google.crypto.tink.shaded.protobuf.Writer$FieldOrder: com.google.crypto.tink.shaded.protobuf.Writer$FieldOrder valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
androidx.appcompat.widget.Toolbar: void setOnMenuItemClickListener(androidx.appcompat.widget.Toolbar$OnMenuItemClickListener)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.FlutterEngine getAttachedFlutterEngine()
go.Seq$RefMap: int roundPow2(int)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization[] values()
androidx.core.view.ViewCompat$Api29Impl: void setContentCaptureSession(android.view.View,androidx.core.view.contentcapture.ContentCaptureSessionCompat)
androidx.appcompat.widget.Toolbar: android.content.Context getPopupContext()
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.recyclerview.widget.RecyclerView: void setScrollState(int)
androidx.appcompat.widget.DropDownListView: void setListSelectionHidden(boolean)
androidx.appcompat.widget.Toolbar: void setPopupTheme(int)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType[] values()
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.view.AccessibilityViewEmbedder: boolean performAction(int,int,android.os.Bundle)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat$CollectionItemInfoCompat buildCollectionItemInfoCompat(boolean,int,int,int,int,boolean,java.lang.String,java.lang.String)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void showOverlaySurface2()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getLogo()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader access$700(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnFrameConsumedListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnFrameConsumedListener)
androidx.profileinstaller.ProfileInstallerInitializer: ProfileInstallerInitializer()
androidx.appcompat.widget.ActionMenuView: void setExpandedActionViewsExclusive(boolean)
androidx.appcompat.widget.AppCompatImageView: void setImageResource(int)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
io.flutter.view.TextureRegistry$ImageTextureEntry: void release()
go.Seq$RefTracker: int inc(java.lang.Object)
androidx.appcompat.widget.Toolbar: void setContentInsetEndWithActions(int)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPlatformMessage(long,java.lang.String,java.nio.ByteBuffer,int,int)
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.core.widget.NestedScrollView: float getVerticalScrollFactorCompat()
androidx.window.extensions.core.util.function.Function: java.lang.Object apply(java.lang.Object)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$302(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState detachState()
go.Seq: void setContext(android.content.Context)
androidx.preference.SwitchPreference: SwitchPreference(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void pruneImageReaderQueue()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImages()
io.flutter.view.TextureRegistry$SurfaceLifecycle: io.flutter.view.TextureRegistry$SurfaceLifecycle[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeLoadDartDeferredLibrary(long,int,java.lang.String[])
androidx.recyclerview.widget.RecyclerView: void setAccessibilityDelegateCompat(androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate)
com.alexmercerind.mediakitandroidhelper.MediaKitAndroidHelper: int openFileDescriptorJava(java.lang.String)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Icon toIcon(androidx.core.graphics.drawable.IconCompat,android.content.Context)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setShortcut(android.view.MenuItem,char,char,int,int)
androidx.core.view.ViewCompat$Api28Impl: java.lang.CharSequence getAccessibilityPaneTitle(android.view.View)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.Image acquireLatestImage()
io.flutter.embedding.engine.FlutterJNI: boolean getIsSoftwareRenderingEnabled()
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void setAsyncWaitForVsyncDelegate(io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate)
androidx.appcompat.widget.Toolbar: int getContentInsetEndWithActions()
io.flutter.embedding.engine.FlutterJNI: void nativePrefetchDefaultFontManager()
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getActiveReader()
androidx.appcompat.widget.ViewStubCompat: void setVisibility(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setTextSelectable(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.recyclerview.widget.RecyclerView: void setItemViewCacheSize(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundResource(int)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo createAccessibilityNodeInfo(int)
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: int getWindowAreaStatus()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.appcompat.widget.ButtonBarLayout: int getMinimumHeight()
androidx.recyclerview.widget.StaggeredGridLayoutManager: StaggeredGridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsRegionalIndicator(int)
com.google.crypto.tink.shaded.protobuf.WireFormat$FieldType: com.google.crypto.tink.shaded.protobuf.WireFormat$FieldType[] values()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void release()
com.kurenai7968.volume_controller.VolumeControllerPlugin: VolumeControllerPlugin()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
androidx.appcompat.widget.SwitchCompat: boolean getSplitTrack()
com.google.crypto.tink.proto.KeyStatusType: com.google.crypto.tink.proto.KeyStatusType valueOf(java.lang.String)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAlignedChildIndex(int)
androidx.appcompat.widget.Toolbar: void setLogo(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetEnd()
androidx.appcompat.widget.SearchView: void setIconifiedByDefault(boolean)
androidx.recyclerview.widget.RecyclerView: boolean getPreserveFocusAfterLayout()
androidx.core.view.ViewCompat$Api29Impl: java.util.List getSystemGestureExclusionRects(android.view.View)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: android.view.accessibility.AccessibilityNodeInfo$ExtraRenderingInfo getExtraRenderingInfo(android.view.accessibility.AccessibilityNodeInfo)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
kotlin.collections.AbstractList: AbstractList()
io.flutter.embedding.engine.FlutterJNI: void hideOverlaySurface2()
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarVisibilityCallback(androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback)
androidx.recyclerview.widget.GridLayoutManager: GridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.core.widget.PopupWindowCompat$Api23Impl: boolean getOverlapAnchor(android.widget.PopupWindow)
com.it_nomads.fluttersecurestorage.ciphers.KeyCipherAlgorithm: com.it_nomads.fluttersecurestorage.ciphers.KeyCipherAlgorithm valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarContainer: void setTransitioning(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader()
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnTrimMemoryListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnTrimMemoryListener)
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.String[] getDigitStrings(android.icu.text.DecimalFormatSymbols)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType[] values()
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType valueOf(java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.core.view.MenuItemCompat$Api26Impl: int getAlphabeticModifiers(android.view.MenuItem)
androidx.appcompat.view.menu.ActionMenuItemView: void setItemInvoker(androidx.appcompat.view.menu.MenuBuilder$ItemInvoker)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory getEdgeEffectFactory()
androidx.appcompat.view.menu.ListMenuItemView: android.view.LayoutInflater getInflater()
androidx.preference.internal.PreferenceImageView: PreferenceImageView(android.content.Context,android.util.AttributeSet)
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api21Impl: void setElevation(android.view.View,float)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
io.flutter.plugin.platform.SingleViewPresentation: void onCreate(android.os.Bundle)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.recyclerview.widget.RecyclerView: void setEdgeEffectFactory(androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory)
androidx.core.content.ContextCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.Context,int)
io.flutter.embedding.engine.FlutterJNI: boolean isAttached()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture()
io.flutter.embedding.engine.FlutterJNI: void registerTexture(long,io.flutter.embedding.engine.renderer.SurfaceTextureWrapper)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMinor()
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.recyclerview.widget.RecyclerView: void setRecyclerListener(androidx.recyclerview.widget.RecyclerView$RecyclerListener)
androidx.appcompat.widget.SearchView: void setQueryHint(java.lang.CharSequence)
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOff()
io.flutter.embedding.android.FlutterImageView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void releaseInternal()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeStepGranularity()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
go.Seq: void init()
androidx.appcompat.widget.ViewStubCompat: android.view.LayoutInflater getLayoutInflater()
androidx.appcompat.widget.Toolbar: void setOverflowIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextView: java.lang.CharSequence getText()
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.core.view.ViewCompat$Api28Impl: boolean isAccessibilityHeading(android.view.View)
androidx.core.widget.PopupWindowCompat$Api23Impl: void setOverlapAnchor(android.widget.PopupWindow,boolean)
androidx.appcompat.widget.ActivityChooserView$InnerLayout: ActivityChooserView$InnerLayout(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api21Impl: void setNestedScrollingEnabled(android.view.View,boolean)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode[] values()
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.ActionMenuPresenter getOuterActionMenuPresenter()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceChanged(long,int,int)
androidx.recyclerview.widget.RecyclerView: void setItemAnimator(androidx.recyclerview.widget.RecyclerView$ItemAnimator)
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness[] values()
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness valueOf(java.lang.String)
com.it_nomads.fluttersecurestorage.ciphers.StorageCipherAlgorithm: com.it_nomads.fluttersecurestorage.ciphers.StorageCipherAlgorithm[] values()
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api26Impl: boolean hasExplicitFocusable(android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
go.Seq$GoRefQueue: Seq$GoRefQueue()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
io.flutter.embedding.engine.FlutterJNI: void nativeSetAccessibilityFeatures(long,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
androidx.core.view.ViewCompat$Api29Impl: android.view.View$AccessibilityDelegate getAccessibilityDelegate(android.view.View)
androidx.recyclerview.widget.RecyclerView: RecyclerView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$800(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo convertToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,int,android.view.View)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedPreFling(android.view.ViewParent,android.view.View,float,float)
io.flutter.embedding.engine.FlutterJNI: void updateDisplayMetrics(int,float,float,float)
androidx.security.crypto.MasterKey$Builder$Api23Impl: java.lang.String getKeystoreAlias(android.security.keystore.KeyGenParameterSpec)
io.flutter.embedding.engine.FlutterJNI: void ensureRunningOnMainThread()
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
com.it_nomads.fluttersecurestorage.ciphers.StorageCipherAlgorithm: com.it_nomads.fluttersecurestorage.ciphers.StorageCipherAlgorithm valueOf(java.lang.String)
go.Seq$Ref: int access$110(go.Seq$Ref)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedFling(android.view.View,float,float,boolean)
androidx.appcompat.widget.SearchView: int getSuggestionCommitIconResId()
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getThumbDrawable()
androidx.appcompat.widget.ActionMenuView: void setOnMenuItemClickListener(androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMajor()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
androidx.appcompat.view.menu.ActionMenuItemView: ActionMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.core.widget.ImageViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getImageTintMode(android.widget.ImageView)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsetsAnimation$Callback getAnimationCallback()
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
androidx.appcompat.view.menu.ListMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImageReaders()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType valueOf(java.lang.String)
io.flutter.view.FlutterCallbackInformation: FlutterCallbackInformation(java.lang.String,java.lang.String,java.lang.String)
com.google.crypto.tink.proto.OutputPrefixType: com.google.crypto.tink.proto.OutputPrefixType[] values()
com.google.crypto.tink.shaded.protobuf.WireFormat$FieldType: com.google.crypto.tink.shaded.protobuf.WireFormat$FieldType valueOf(java.lang.String)
androidx.preference.internal.PreferenceImageView: void setMaxWidth(int)
androidx.appcompat.widget.SearchView: void setAppSearchData(android.os.Bundle)
androidx.appcompat.view.menu.ActionMenuItemView: void setTitle(java.lang.CharSequence)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle getLifecycle()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: android.view.WindowInsets onProgress(android.view.WindowInsets,java.util.List)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
androidx.core.view.ViewConfigurationCompat$Api28Impl: boolean shouldShowMenuShortcutsWhenKeyboardPresent(android.view.ViewConfiguration)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.widget.NestedScrollView: void setOnScrollChangeListener(androidx.core.widget.NestedScrollView$OnScrollChangeListener)
io.flutter.embedding.engine.FlutterJNI: void deferredComponentInstallFailure(int,java.lang.String,boolean)
com.alexmercerind.mediakitandroidhelper.MediaKitAndroidHelper: long newGlobalObjectRef(java.lang.Object)
androidx.preference.SwitchPreferenceCompat: SwitchPreferenceCompat(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.ProcessLifecycleOwner$Api29Impl: void registerActivityLifecycleCallbacks(android.app.Activity,android.app.Application$ActivityLifecycleCallbacks)
androidx.core.content.res.ResourcesCompat$Api23Impl: int getColor(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.appcompat.widget.AppCompatImageButton: void setImageDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void attachToGLContext(int)
androidx.core.content.FileProvider$Api21Impl: java.io.File[] getExternalMediaDirs(android.content.Context)
io.flutter.view.TextureRegistry$SurfaceProducer: int getWidth()
androidx.appcompat.widget.Toolbar: android.view.MenuInflater getMenuInflater()
androidx.core.view.ViewCompat$Api21Impl: boolean startNestedScroll(android.view.View,int)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float getDistance(android.widget.EdgeEffect)
androidx.preference.MultiSelectListPreference: MultiSelectListPreference(android.content.Context,android.util.AttributeSet)
androidx.core.graphics.drawable.IconCompat$Api28Impl: android.net.Uri getUri(java.lang.Object)
kotlinx.coroutines.android.AndroidDispatcherFactory: java.lang.String hintOnError()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean shouldUpdate()
androidx.appcompat.widget.ActionBarContainer: void setVisibility(int)
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
com.example.my_115_app.MainActivity: MainActivity()
androidx.appcompat.widget.SearchView: void setQueryRefinementEnabled(boolean)
androidx.appcompat.widget.Toolbar: void setContentInsetStartWithNavigation(int)
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase[] values()
io.flutter.embedding.engine.FlutterJNI: void onDisplayOverlaySurface(int,int,int,int,int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceWindowChanged(long,android.view.Surface)
androidx.preference.PreferenceGroup: PreferenceGroup(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode valueOf(java.lang.String)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushTransform(float[])
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterTexture(long,long,java.lang.ref.WeakReference)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType[] values()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult[] values()
androidx.appcompat.widget.ViewStubCompat: ViewStubCompat(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: long nativeAttach(io.flutter.embedding.engine.FlutterJNI)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsVariationSelector(int)
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface getSurface()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode[] values()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
kotlinx.coroutines.android.AndroidDispatcherFactory: int getLoadPriority()
com.google.crypto.tink.proto.KeyStatusType: com.google.crypto.tink.proto.KeyStatusType[] values()
androidx.appcompat.widget.ActionBarOverlayLayout: void setHasNonEmbeddedTabs(boolean)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: ProcessLifecycleOwner$attach$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushOpacity(float)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterOverlaySurface createOverlaySurface()
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: void setPresentationView(android.view.View)
androidx.core.content.ContextCompat$Api21Impl: java.io.File getCodeCacheDir(android.content.Context)
androidx.appcompat.widget.Toolbar: android.widget.TextView getSubtitleTextView()
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
com.alexmercerind.mediakitandroidhelper.MediaKitAndroidHelper: void deleteGlobalObjectRef(long)
io.flutter.embedding.engine.FlutterJNI: void removeEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat[] values()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void applyTransactions()
androidx.window.extensions.core.util.function.Consumer: void accept(java.lang.Object)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostResumed(android.app.Activity)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: void setNavigationIcon(int)
go.Seq$RefMap: void put(int,go.Seq$Ref)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader33()
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageEmptyResponseCallback(int)
androidx.appcompat.widget.FitWindowsFrameLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.core.view.ViewCompat$Api21Impl: void stopNestedScroll(android.view.View)
androidx.core.view.ViewConfigurationCompat$Api28Impl: int getScaledHoverSlop(android.view.ViewConfiguration)
androidx.appcompat.widget.SearchView: int getInputType()
androidx.preference.PreferenceScreen: PreferenceScreen(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.FitWindowsLinearLayout: FitWindowsLinearLayout(android.content.Context,android.util.AttributeSet)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTint(android.graphics.drawable.Drawable,int)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
io.flutter.embedding.engine.FlutterOverlaySurface: int getId()
androidx.appcompat.widget.ViewStubCompat: int getInflatedId()
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: boolean setLayoutDirection(android.graphics.drawable.Drawable,int)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspot(android.graphics.drawable.Drawable,float,float)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode[] values()
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet,int)
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$500(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.Toolbar: int getTitleMarginBottom()
androidx.appcompat.view.menu.ListMenuItemView: void setCheckable(boolean)
androidx.security.crypto.EncryptedSharedPreferences$EncryptedType: androidx.security.crypto.EncryptedSharedPreferences$EncryptedType valueOf(java.lang.String)
androidx.core.widget.PopupWindowCompat$Api23Impl: void setWindowLayoutType(android.widget.PopupWindow,int)
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode valueOf(java.lang.String)
androidx.preference.DialogPreference: DialogPreference(android.content.Context,android.util.AttributeSet)
androidx.core.content.res.ResourcesCompat$Api23Impl: android.content.res.ColorStateList getColorStateList(android.content.res.Resources,int,android.content.res.Resources$Theme)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchEmptyPlatformMessage(long,java.lang.String,int)
io.flutter.embedding.engine.FlutterJNI: void swapTransactions()
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
io.flutter.embedding.engine.FlutterJNI: void ensureAttachedToNative()
io.flutter.embedding.engine.FlutterJNI: void nativeImageHeaderCallback(long,int,int)
androidx.appcompat.widget.ActionMenuView: ActionMenuView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getSubtitle()
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingLeft()
androidx.appcompat.widget.Toolbar: void setCollapsible(boolean)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType[] values()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$300(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.widget.NestedScrollView$Api21Impl: boolean getClipToPadding(android.view.ViewGroup)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateJavaAssetManager(long,android.content.res.AssetManager,java.lang.String)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getContentDescription(android.view.MenuItem)
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: android.util.DisplayMetrics getWindowAreaDisplayMetrics()
androidx.preference.internal.PreferenceImageView: void setMaxHeight(int)
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
com.aaassseee.screen_brightness_android.ScreenBrightnessAndroidPlugin: ScreenBrightnessAndroidPlugin()
dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin: WakelockPlusPlugin()
io.flutter.embedding.engine.FlutterJNI: void onPreEngineRestart()
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setSearchView(androidx.appcompat.widget.SearchView)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
androidx.core.view.ViewCompat$Api21Impl: void setZ(android.view.View,float)
androidx.core.view.MenuItemCompat$Api26Impl: android.graphics.PorterDuff$Mode getIconTintMode(android.view.MenuItem)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo,long)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.core.view.ViewCompat$Api30Impl: void setImportantForContentCapture(android.view.View,int)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetRight(android.view.DisplayCutout)
go.Seq: go.Seq$Ref getRef(int)
io.flutter.embedding.engine.FlutterJNI: void onDisplayPlatformView2(int,int,int,int,int,int,int,io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack)
go.Seq: int incRef(java.lang.Object)
androidx.appcompat.widget.ActionBarOverlayLayout: java.lang.CharSequence getTitle()
androidx.appcompat.widget.ActionBarContextView: void setSubtitle(java.lang.CharSequence)
androidx.profileinstaller.ProfileInstallerInitializer$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void startRearDisplaySession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
androidx.appcompat.widget.SwitchCompat: boolean getShowText()
androidx.preference.Preference: Preference(android.content.Context,android.util.AttributeSet)
androidx.core.widget.NestedScrollView: float getBottomFadingEdgeStrength()
androidx.appcompat.widget.AppCompatTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: void setViewportMetrics(float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void lambda$dequeueImage$0()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$300(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getNavigationContentDescription()
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScrollAccepted(android.view.ViewParent,android.view.View,android.view.View,int)
io.flutter.embedding.engine.FlutterJNI: void setDeferredComponentManager(io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager)
io.flutter.embedding.engine.FlutterJNI: boolean IsSurfaceControlEnabled()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
androidx.core.view.ViewCompat$Api30Impl: void setStateDescription(android.view.View,java.lang.CharSequence)
androidx.appcompat.widget.AppCompatTextView: void setTextFuture(java.util.concurrent.Future)
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMinimumFlingVelocity(android.view.ViewConfiguration,int,int,int)
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder[] values()
androidx.core.view.ViewCompat$Api21Impl: void setTransitionName(android.view.View,java.lang.String)
